package subscriber

import (
	"context"
	"database/sql"
	"encoding/json"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	amqp "github.com/rabbitmq/amqp091-go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/repository"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type mockLogger struct{}

func (m *mockLogger) Debug(msg string, fields ...vlog.Field) {}
func (m *mockLogger) Info(msg string, fields ...vlog.Field)  {}
func (m *mockLogger) Warn(msg string, fields ...vlog.Field)  {}
func (m *mockLogger) Error(msg string, fields ...vlog.Field) {}
func (m *mockLogger) Fatal(msg string, fields ...vlog.Field) {}
func (m *mockLogger) Panic(msg string, fields ...vlog.Field) {}
func (m *mockLogger) Flush() error {
	return nil
}
func (m *mockLogger) Log(level vlog.Level, msg string, fields ...vlog.Field) {}
func (m *mockLogger) With(fields ...vlog.Field) vlog.Logger {
	return m
}

func TestNewRatingSubscriber(t *testing.T) {
	logger := &mockLogger{}
	queueName := "test-rating-queue"

	subscriber := NewRatingSubscriber(queueName, logger)

	assert.NotNil(t, subscriber)
	assert.Equal(t, queueName, subscriber.Queue)
	assert.Equal(t, logger, subscriber.logger)
	assert.Nil(t, subscriber.DB)
}

func TestRatingSubscriber_QueueName(t *testing.T) {
	logger := &mockLogger{}
	queueName := "test-rating-queue"

	subscriber := NewRatingSubscriber(queueName, logger)

	assert.Equal(t, queueName, subscriber.QueueName())
}

func TestRatingSubscriber_SetDB(t *testing.T) {
	logger := &mockLogger{}
	queueName := "test-rating-queue"

	// Create mock database
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	subscriber := NewRatingSubscriber(queueName, logger)
	subscriber.SetDB(db)

	assert.Equal(t, db, subscriber.DB)
}

func TestRatingSubscriber_HandleMessage_Success(t *testing.T) {
	logger := &mockLogger{}
	queueName := "test-rating-queue"

	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	subscriber := NewRatingSubscriber(queueName, logger)
	subscriber.SetDB(db)

	// Create test logbook
	logbook := repository.Logbooks{
		ID:                 "test-logbook-id",
		EventID:            "test-event-id",
		FromRole:           "user",
		FromEntityID:       "test-from-entity",
		ToRole:             "pro",
		ToEntityID:         "test-to-entity",
		FieldTag:           "service",
		StarRating:         5,
		OverallExperience:  "Excellent",
		EngagementSkills:   "Outstanding",
		ProfessionalSkills: "Great",
		PersonalSkills:     "Amazing",
		ThankingNote:       "Thank you",
		FormData:           map[string]interface{}{"key": "value"},
		SubmittedAt:        time.Now(),
		CorrelationID:      "test-correlation-id",
	}

	logbookJSON, err := json.Marshal(logbook)
	require.NoError(t, err)

	// Create AMQP delivery
	delivery := amqp.Delivery{
		Body:          logbookJSON,
		CorrelationId: "test-correlation-id",
	}

	mock.ExpectBegin()

	mock.ExpectExec(`INSERT INTO log_books`).
		WithArgs(
			logbook.ID,
			logbook.EventID,
			logbook.FromRole,
			logbook.FromEntityID,
			logbook.ToRole,
			logbook.ToEntityID,
			logbook.FieldTag,
			logbook.StarRating,
			logbook.OverallExperience,
			logbook.ProfessionalSkills,
			logbook.PersonalSkills,
			logbook.ThankingNote,
			sqlmock.AnyArg(), // form_data JSON
			sqlmock.AnyArg(), // submitted_at time
			logbook.CorrelationID,
			logbook.EngagementSkills,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	mock.ExpectCommit()

	ctx := context.Background()
	err = subscriber.HandleMessage(ctx, delivery)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestRatingSubscriber_HandleMessage_InvalidJSON(t *testing.T) {
	logger := &mockLogger{}
	queueName := "test-rating-queue"

	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	subscriber := NewRatingSubscriber(queueName, logger)
	subscriber.SetDB(db)

	delivery := amqp.Delivery{
		Body:          []byte("invalid json"),
		CorrelationId: "test-correlation-id",
	}

	ctx := context.Background()
	err = subscriber.HandleMessage(ctx, delivery)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid character")
}

func TestRatingSubscriber_HandleMessage_EmptyCorrelationID(t *testing.T) {
	logger := &mockLogger{}
	queueName := "test-rating-queue"

	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	subscriber := NewRatingSubscriber(queueName, logger)
	subscriber.SetDB(db)

	//logbook without correlation ID
	logbook := repository.Logbooks{
		ID:                 "test-logbook-id",
		EventID:            "test-event-id",
		FromRole:           "user",
		FromEntityID:       "test-from-entity",
		ToRole:             "pro",
		ToEntityID:         "test-to-entity",
		FieldTag:           "service",
		StarRating:         5,
		OverallExperience:  "Excellent",
		EngagementSkills:   "Outstanding",
		ProfessionalSkills: "Great",
		PersonalSkills:     "Amazing",
		ThankingNote:       "Thank you",
		FormData:           map[string]interface{}{"key": "value"},
		SubmittedAt:        time.Now(),
		CorrelationID:      "",
	}

	logbookJSON, err := json.Marshal(logbook)
	require.NoError(t, err)

	delivery := amqp.Delivery{
		Body:          logbookJSON,
		CorrelationId: "delivery-correlation-id",
	}

	mock.ExpectBegin()

	mock.ExpectExec(`INSERT INTO log_books`).
		WithArgs(
			logbook.ID,
			logbook.EventID,
			logbook.FromRole,
			logbook.FromEntityID,
			logbook.ToRole,
			logbook.ToEntityID,
			logbook.FieldTag,
			logbook.StarRating,
			logbook.OverallExperience,
			logbook.ProfessionalSkills,
			logbook.PersonalSkills,
			logbook.ThankingNote,
			sqlmock.AnyArg(),
			sqlmock.AnyArg(),
			"delivery-correlation-id",
			logbook.EngagementSkills,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	mock.ExpectCommit()

	ctx := context.Background()
	err = subscriber.HandleMessage(ctx, delivery)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestRatingSubscriber_HandleMessage_DatabaseError(t *testing.T) {
	logger := &mockLogger{}
	queueName := "test-rating-queue"

	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	subscriber := NewRatingSubscriber(queueName, logger)
	subscriber.SetDB(db)

	logbook := repository.Logbooks{
		ID:                 "test-logbook-id",
		EventID:            "test-event-id",
		FromRole:           "user",
		FromEntityID:       "test-from-entity",
		ToRole:             "pro",
		ToEntityID:         "test-to-entity",
		FieldTag:           "service",
		StarRating:         5,
		OverallExperience:  "Excellent",
		EngagementSkills:   "Outstanding",
		ProfessionalSkills: "Great",
		PersonalSkills:     "Amazing",
		ThankingNote:       "Thank you",
		FormData:           map[string]interface{}{"key": "value"},
		SubmittedAt:        time.Now(),
		CorrelationID:      "test-correlation-id",
	}

	logbookJSON, err := json.Marshal(logbook)
	require.NoError(t, err)

	delivery := amqp.Delivery{
		Body:          logbookJSON,
		CorrelationId: "test-correlation-id",
	}

	mock.ExpectBegin().WillReturnError(sql.ErrConnDone)

	ctx := context.Background()
	err = subscriber.HandleMessage(ctx, delivery)

	assert.Error(t, err)
	assert.Equal(t, sql.ErrConnDone, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestRatingSubscriber_HandleMessage_WithNilFormData(t *testing.T) {
	logger := &mockLogger{}
	queueName := "test-rating-queue"

	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	subscriber := NewRatingSubscriber(queueName, logger)
	subscriber.SetDB(db)

	//logbook with nil FormData
	logbook := repository.Logbooks{
		ID:                 "test-logbook-id",
		EventID:            "test-event-id",
		FromRole:           "user",
		FromEntityID:       "test-from-entity",
		ToRole:             "pro",
		ToEntityID:         "test-to-entity",
		FieldTag:           "service",
		StarRating:         5,
		OverallExperience:  "Excellent",
		EngagementSkills:   "Outstanding",
		ProfessionalSkills: "Great",
		PersonalSkills:     "Amazing",
		ThankingNote:       "Thank you",
		FormData:           nil,
		SubmittedAt:        time.Now(),
		CorrelationID:      "test-correlation-id",
	}

	logbookJSON, err := json.Marshal(logbook)
	require.NoError(t, err)

	delivery := amqp.Delivery{
		Body:          logbookJSON,
		CorrelationId: "test-correlation-id",
	}

	mock.ExpectBegin()

	mock.ExpectExec(`INSERT INTO log_books`).
		WithArgs(
			logbook.ID,
			logbook.EventID,
			logbook.FromRole,
			logbook.FromEntityID,
			logbook.ToRole,
			logbook.ToEntityID,
			logbook.FieldTag,
			logbook.StarRating,
			logbook.OverallExperience,
			logbook.ProfessionalSkills,
			logbook.PersonalSkills,
			logbook.ThankingNote,
			sqlmock.AnyArg(),
			sqlmock.AnyArg(),
			logbook.CorrelationID,
			logbook.EngagementSkills,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	mock.ExpectCommit()

	ctx := context.Background()
	err = subscriber.HandleMessage(ctx, delivery)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestRatingSubscriber_HandleMessage_WithEmptyDeliveryCorrelationID(t *testing.T) {
	logger := &mockLogger{}
	queueName := "test-rating-queue"

	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	subscriber := NewRatingSubscriber(queueName, logger)
	subscriber.SetDB(db)

	//logbook with correlation ID
	logbook := repository.Logbooks{
		ID:                 "test-logbook-id",
		EventID:            "test-event-id",
		FromRole:           "user",
		FromEntityID:       "test-from-entity",
		ToRole:             "pro",
		ToEntityID:         "test-to-entity",
		FieldTag:           "service",
		StarRating:         5,
		OverallExperience:  "Excellent",
		EngagementSkills:   "Outstanding",
		ProfessionalSkills: "Great",
		PersonalSkills:     "Amazing",
		ThankingNote:       "Thank you",
		FormData:           map[string]interface{}{"key": "value"},
		SubmittedAt:        time.Now(),
		CorrelationID:      "logbook-correlation-id",
	}

	logbookJSON, err := json.Marshal(logbook)
	require.NoError(t, err)

	delivery := amqp.Delivery{
		Body:          logbookJSON,
		CorrelationId: "",
	}

	mock.ExpectBegin()

	mock.ExpectExec(`INSERT INTO log_books`).
		WithArgs(
			logbook.ID,
			logbook.EventID,
			logbook.FromRole,
			logbook.FromEntityID,
			logbook.ToRole,
			logbook.ToEntityID,
			logbook.FieldTag,
			logbook.StarRating,
			logbook.OverallExperience,
			logbook.ProfessionalSkills,
			logbook.PersonalSkills,
			logbook.ThankingNote,
			sqlmock.AnyArg(),
			sqlmock.AnyArg(),
			"logbook-correlation-id",
			logbook.EngagementSkills,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	mock.ExpectCommit()

	ctx := context.Background()
	err = subscriber.HandleMessage(ctx, delivery)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}
