package subscriber

import (
	"context"
	"database/sql"
	"encoding/json"

	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/repository"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type DeleteRatingSubscriber struct {
	DB        *sql.DB
	queueName string
}

func NewDeleteRatingSubscriber(db *sql.DB, queueName string) *DeleteRatingSubscriber {
	return &DeleteRatingSubscriber{
		DB:        db,
		queueName: queueName,
	}
}

func (s *DeleteRatingSubscriber) QueueName() string {
	return s.queueName
}

func (s *DeleteRatingSubscriber) Handle(ctx context.Context, delivery amqp.Delivery) error {
	logger := vlog.FromContext(ctx).With(
		vlog.F("subscriber", "DeleteRatingSubscriber"),
		vlog.F("correlation_id", delivery.CorrelationId),
	)
	logger.Debug("received delete rating message", vlog.F("body", string(delivery.Body)))

	// Parse the delete rating message
	var deleteMessage usecase.DeleteRatingMessage
	if err := json.Unmarshal(delivery.Body, &deleteMessage); err != nil {
		logger.Error("failed to unmarshal delete rating message", vlog.F("error", err))
		return err
	}

	logger.Info("processing delete rating request", 
		vlog.F("event_id", deleteMessage.EventID),
		vlog.F("action", deleteMessage.Action),
		vlog.F("correlation_id", deleteMessage.CorrelationID))

	// Validate the message
	if deleteMessage.EventID == "" {
		logger.Error("event_id is required in delete message")
		return nil // Don't requeue invalid messages
	}

	if deleteMessage.Action != "delete_rating" {
		logger.Error("invalid action in delete message", vlog.F("action", deleteMessage.Action))
		return nil // Don't requeue invalid messages
	}

	// Delete the rating from the database
	result, err := repository.DeleteRatingByEventWithDB(ctx, s.DB, deleteMessage.EventID)
	if err != nil {
		logger.Error("failed to delete rating from database", 
			vlog.F("error", err),
			vlog.F("event_id", deleteMessage.EventID))
		return err // This will cause the message to be requeued
	}

	logger.Info("rating deleted successfully", 
		vlog.F("event_id", deleteMessage.EventID),
		vlog.F("deleted_count", result.DeletedCount),
		vlog.F("correlation_id", deleteMessage.CorrelationID))

	// Acknowledge the message
	return nil
}
