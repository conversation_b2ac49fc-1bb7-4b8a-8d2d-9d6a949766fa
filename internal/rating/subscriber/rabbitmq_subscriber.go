package subscriber

import (
	"context"
	"database/sql"
	"encoding/json"

	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/repository"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// Logbook Interaction represents the expected JSON payload and DB table
type Logbook = repository.Logbooks

type RatingSubscriber struct {
	DB     *sql.DB
	Queue  string
	logger vlog.Logger
}

func NewRatingSubscriber(queueName string, logger vlog.Logger) *RatingSubscriber {
	return &RatingSubscriber{
		Queue:  queueName,
		logger: logger,
	}
}

func (s *RatingSubscriber) QueueName() string {
	return s.Queue
}

// Set the database connection
func (s *RatingSubscriber) SetDB(db *sql.DB) {
	s.DB = db
}

func (s *RatingSubscriber) HandleMessage(ctx context.Context, delivery amqp.Delivery) error {
	s.logger.Info("################---- received rating message --- ######################",
		vlog.F("correlation_id", delivery.CorrelationId),
		vlog.F("queue", s.QueueName()))

	var logbook Logbook
	if err := json.Unmarshal(delivery.Body, &logbook); err != nil {
		s.logger.Error("Failed to unmarshal logbook interaction", vlog.F("error", err))
		return err
	}

	if logbook.CorrelationID == "" {
		logbook.CorrelationID = delivery.CorrelationId
	}

	return repository.InsertlogbooksWithLogBook(ctx, s.DB, &logbook)
}
