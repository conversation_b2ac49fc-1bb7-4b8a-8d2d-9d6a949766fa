package subscriber

import (
	"context"
	"database/sql"
	"encoding/json"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	amqp "github.com/rabbitmq/amqp091-go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func TestDeleteRatingSubscriber_Handle_Success(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	queueName := "test-delete-queue"
	subscriber := NewDeleteRatingSubscriber(db, queueName)

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	t.Run("success_single_record_deleted", func(t *testing.T) {
		deleteMessage := usecase.DeleteRatingMessage{
			EventID:       "test-event-id",
			Action:        "delete_rating",
			CorrelationID: "test-correlation-id",
			RequestedAt:   time.Now(),
		}

		messageBody, err := json.Marshal(deleteMessage)
		require.NoError(t, err)

		delivery := amqp.Delivery{
			Body:          messageBody,
			CorrelationId: deleteMessage.CorrelationID,
		}

		// Mock successful delete
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM log_books WHERE event_id = \$1`).
			WithArgs(deleteMessage.EventID).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		err = subscriber.Handle(ctx, delivery)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_multiple_records_deleted", func(t *testing.T) {
		deleteMessage := usecase.DeleteRatingMessage{
			EventID:       "test-event-id-multiple",
			Action:        "delete_rating",
			CorrelationID: "test-correlation-id-2",
			RequestedAt:   time.Now(),
		}

		messageBody, err := json.Marshal(deleteMessage)
		require.NoError(t, err)

		delivery := amqp.Delivery{
			Body:          messageBody,
			CorrelationId: deleteMessage.CorrelationID,
		}

		// Mock successful delete of multiple records
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM log_books WHERE event_id = \$1`).
			WithArgs(deleteMessage.EventID).
			WillReturnResult(sqlmock.NewResult(0, 3))
		mock.ExpectCommit()

		err = subscriber.Handle(ctx, delivery)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_no_records_found", func(t *testing.T) {
		deleteMessage := usecase.DeleteRatingMessage{
			EventID:       "non-existent-event-id",
			Action:        "delete_rating",
			CorrelationID: "test-correlation-id-3",
			RequestedAt:   time.Now(),
		}

		messageBody, err := json.Marshal(deleteMessage)
		require.NoError(t, err)

		delivery := amqp.Delivery{
			Body:          messageBody,
			CorrelationId: deleteMessage.CorrelationID,
		}

		// Mock delete with no records found
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM log_books WHERE event_id = \$1`).
			WithArgs(deleteMessage.EventID).
			WillReturnResult(sqlmock.NewResult(0, 0))
		mock.ExpectCommit()

		err = subscriber.Handle(ctx, delivery)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteRatingSubscriber_Handle_Errors(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	queueName := "test-delete-queue"
	subscriber := NewDeleteRatingSubscriber(db, queueName)

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	t.Run("invalid_json", func(t *testing.T) {
		delivery := amqp.Delivery{
			Body:          []byte("invalid json"),
			CorrelationId: "test-correlation-id",
		}

		err = subscriber.Handle(ctx, delivery)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("empty_event_id", func(t *testing.T) {
		deleteMessage := usecase.DeleteRatingMessage{
			EventID:       "", // Empty event ID
			Action:        "delete_rating",
			CorrelationID: "test-correlation-id",
			RequestedAt:   time.Now(),
		}

		messageBody, err := json.Marshal(deleteMessage)
		require.NoError(t, err)

		delivery := amqp.Delivery{
			Body:          messageBody,
			CorrelationId: deleteMessage.CorrelationID,
		}

		// No database expectations since it should return early
		err = subscriber.Handle(ctx, delivery)
		assert.NoError(t, err) // Should not error, just log and return
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("invalid_action", func(t *testing.T) {
		deleteMessage := usecase.DeleteRatingMessage{
			EventID:       "test-event-id",
			Action:        "invalid_action", // Invalid action
			CorrelationID: "test-correlation-id",
			RequestedAt:   time.Now(),
		}

		messageBody, err := json.Marshal(deleteMessage)
		require.NoError(t, err)

		delivery := amqp.Delivery{
			Body:          messageBody,
			CorrelationId: deleteMessage.CorrelationID,
		}

		// No database expectations since it should return early
		err = subscriber.Handle(ctx, delivery)
		assert.NoError(t, err) // Should not error, just log and return
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("database_error", func(t *testing.T) {
		deleteMessage := usecase.DeleteRatingMessage{
			EventID:       "test-event-id",
			Action:        "delete_rating",
			CorrelationID: "test-correlation-id",
			RequestedAt:   time.Now(),
		}

		messageBody, err := json.Marshal(deleteMessage)
		require.NoError(t, err)

		delivery := amqp.Delivery{
			Body:          messageBody,
			CorrelationId: deleteMessage.CorrelationID,
		}

		// Mock database error
		mock.ExpectBegin().WillReturnError(sql.ErrConnDone)

		err = subscriber.Handle(ctx, delivery)
		assert.Error(t, err)
		assert.Equal(t, sql.ErrConnDone, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("delete_query_error", func(t *testing.T) {
		deleteMessage := usecase.DeleteRatingMessage{
			EventID:       "test-event-id",
			Action:        "delete_rating",
			CorrelationID: "test-correlation-id",
			RequestedAt:   time.Now(),
		}

		messageBody, err := json.Marshal(deleteMessage)
		require.NoError(t, err)

		delivery := amqp.Delivery{
			Body:          messageBody,
			CorrelationId: deleteMessage.CorrelationID,
		}

		// Mock delete query error
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM log_books WHERE event_id = \$1`).
			WithArgs(deleteMessage.EventID).
			WillReturnError(sql.ErrTxDone)
		mock.ExpectRollback()

		err = subscriber.Handle(ctx, delivery)
		assert.Error(t, err)
		assert.Equal(t, sql.ErrTxDone, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteRatingSubscriber_QueueName(t *testing.T) {
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	queueName := "test-delete-queue"
	subscriber := NewDeleteRatingSubscriber(db, queueName)

	t.Run("returns_correct_queue_name", func(t *testing.T) {
		assert.Equal(t, queueName, subscriber.QueueName())
	})
}

func TestNewDeleteRatingSubscriber(t *testing.T) {
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	queueName := "test-delete-queue"

	t.Run("success", func(t *testing.T) {
		subscriber := NewDeleteRatingSubscriber(db, queueName)
		assert.NotNil(t, subscriber)
		assert.Equal(t, db, subscriber.DB)
		assert.Equal(t, queueName, subscriber.QueueName())
	})

	t.Run("with_nil_db", func(t *testing.T) {
		subscriber := NewDeleteRatingSubscriber(nil, queueName)
		assert.NotNil(t, subscriber)
		assert.Nil(t, subscriber.DB)
		assert.Equal(t, queueName, subscriber.QueueName())
	})

	t.Run("with_empty_queue_name", func(t *testing.T) {
		subscriber := NewDeleteRatingSubscriber(db, "")
		assert.NotNil(t, subscriber)
		assert.Equal(t, db, subscriber.DB)
		assert.Equal(t, "", subscriber.QueueName())
	})
}

func TestDeleteRatingSubscriber_Handle_MessageParsing(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	queueName := "test-delete-queue"
	subscriber := NewDeleteRatingSubscriber(db, queueName)

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	t.Run("verify_message_parsing", func(t *testing.T) {
		eventID := "test-event-id-parsing"
		correlationID := "test-correlation-id-parsing"
		requestedAt := time.Now()

		deleteMessage := usecase.DeleteRatingMessage{
			EventID:       eventID,
			Action:        "delete_rating",
			CorrelationID: correlationID,
			RequestedAt:   requestedAt,
		}

		messageBody, err := json.Marshal(deleteMessage)
		require.NoError(t, err)

		delivery := amqp.Delivery{
			Body:          messageBody,
			CorrelationId: correlationID,
		}

		// Mock successful delete
		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM log_books WHERE event_id = \$1`).
			WithArgs(eventID).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		err = subscriber.Handle(ctx, delivery)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
