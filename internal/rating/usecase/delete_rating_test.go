package usecase_test

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"testing"

	amqp "github.com/rabbitmq/amqp091-go"
	"github.com/stretchr/testify/assert"
	uc "gitlab.viswalslab.com/backend/ratings/internal/rating/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
	"go.uber.org/mock/gomock"
)

// Mock RabbitMQ publisher for testing delete rating
type mockDeleteRatingPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockDeleteRatingPublisherMockRecorder
}

type MockDeleteRatingPublisherMockRecorder struct {
	mock *mockDeleteRatingPublisher
}

func NewMockDeleteRatingPublisher(ctrl *gomock.Controller) *mockDeleteRatingPublisher {
	mock := &mockDeleteRatingPublisher{ctrl: ctrl}
	mock.recorder = &MockDeleteRatingPublisherMockRecorder{mock}
	return mock
}

func (m *mockDeleteRatingPublisher) EXPECT() *MockDeleteRatingPublisherMockRecorder {
	return m.recorder
}

func (m *mockDeleteRatingPublisher) Publish(ctx context.Context, exchangeName string, key string, publishing amqp.Publishing) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Publish", ctx, exchangeName, key, publishing)
	ret0, _ := ret[0].(error)
	return ret0
}

func (mr *MockDeleteRatingPublisherMockRecorder) Publish(ctx, exchangeName, key, publishing interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*mockDeleteRatingPublisher)(nil).Publish), ctx, exchangeName, key, publishing)
}

func TestDeleteRatingUsecase_Execute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	mockPublisher := NewMockDeleteRatingPublisher(ctrl)
	queueName := "test-delete-rating-queue"
	u := uc.NewDeleteRatingUsecase(mockPublisher, queueName)

	t.Run("success", func(t *testing.T) {
		input := &uc.DeleteRatingInput{
			EventID: "test-event-id",
		}

		// Mock successful publish
		mockPublisher.EXPECT().Publish(
			gomock.Any(),
			"",
			queueName,
			gomock.Any(),
		).Return(nil)

		output, err := u.Execute(ctx, input)
		assert.NoError(t, err)
		assert.NotNil(t, output)
		assert.True(t, output.Success)
		assert.NotEmpty(t, output.CorrelationID)
		assert.Equal(t, "Delete rating request submitted successfully", output.Message)
		assert.Equal(t, input.EventID, output.EventID)
	})

	t.Run("rabbitmq_publish_error", func(t *testing.T) {
		input := &uc.DeleteRatingInput{
			EventID: "test-event-id",
		}

		publishError := errors.New("rabbitmq connection failed")
		mockPublisher.EXPECT().Publish(
			gomock.Any(),
			"",
			queueName,
			gomock.Any(),
		).Return(publishError)

		output, err := u.Execute(ctx, input)
		assert.Error(t, err)
		assert.Nil(t, output)
		assert.Equal(t, publishError, err)
	})

	t.Run("verify_message_structure", func(t *testing.T) {
		input := &uc.DeleteRatingInput{
			EventID: "test-event-id",
		}

		// Capture the published message
		var capturedPublishing amqp.Publishing
		mockPublisher.EXPECT().Publish(
			gomock.Any(),
			"",
			queueName,
			gomock.Any(),
		).DoAndReturn(func(ctx context.Context, exchangeName string, key string, publishing amqp.Publishing) error {
			capturedPublishing = publishing
			return nil
		})

		output, err := u.Execute(ctx, input)
		assert.NoError(t, err)
		assert.NotNil(t, output)

		// Verify message structure
		assert.Equal(t, "application/json", capturedPublishing.ContentType)
		assert.NotEmpty(t, capturedPublishing.CorrelationId)
		assert.NotZero(t, capturedPublishing.Timestamp)

		// Unmarshal and verify the message body
		var deleteMessage uc.DeleteRatingMessage
		err = json.Unmarshal(capturedPublishing.Body, &deleteMessage)
		assert.NoError(t, err)
		assert.Equal(t, input.EventID, deleteMessage.EventID)
		assert.Equal(t, "delete_rating", deleteMessage.Action)
		assert.Equal(t, output.CorrelationID, deleteMessage.CorrelationID)
		assert.NotZero(t, deleteMessage.RequestedAt)
	})

	t.Run("empty_event_id", func(t *testing.T) {
		input := &uc.DeleteRatingInput{
			EventID: "",
		}

		// Mock successful publish (the usecase doesn't validate empty event_id)
		mockPublisher.EXPECT().Publish(
			gomock.Any(),
			"",
			queueName,
			gomock.Any(),
		).Return(nil)

		output, err := u.Execute(ctx, input)
		assert.NoError(t, err)
		assert.NotNil(t, output)
		assert.True(t, output.Success)
		assert.Equal(t, "", output.EventID)
	})

	t.Run("verify_correlation_id_uniqueness", func(t *testing.T) {
		input := &uc.DeleteRatingInput{
			EventID: "test-event-id",
		}

		var correlationIDs []string
		mockPublisher.EXPECT().Publish(
			gomock.Any(),
			"",
			queueName,
			gomock.Any(),
		).DoAndReturn(func(ctx context.Context, exchangeName string, key string, publishing amqp.Publishing) error {
			correlationIDs = append(correlationIDs, publishing.CorrelationId)
			return nil
		}).Times(3)

		// Execute multiple times to verify unique correlation IDs
		for i := 0; i < 3; i++ {
			output, err := u.Execute(ctx, input)
			assert.NoError(t, err)
			assert.NotNil(t, output)
		}

		// Verify all correlation IDs are unique
		assert.Len(t, correlationIDs, 3)
		assert.NotEqual(t, correlationIDs[0], correlationIDs[1])
		assert.NotEqual(t, correlationIDs[1], correlationIDs[2])
		assert.NotEqual(t, correlationIDs[0], correlationIDs[2])
	})
}

func TestNewDeleteRatingUsecase(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockPublisher := NewMockDeleteRatingPublisher(ctrl)
	queueName := "test-delete-rating-queue"

	t.Run("success", func(t *testing.T) {
		usecase := uc.NewDeleteRatingUsecase(mockPublisher, queueName)
		assert.NotNil(t, usecase)
	})

	t.Run("with_nil_publisher", func(t *testing.T) {
		usecase := uc.NewDeleteRatingUsecase(nil, queueName)
		assert.NotNil(t, usecase)
	})

	t.Run("with_empty_queue_name", func(t *testing.T) {
		usecase := uc.NewDeleteRatingUsecase(mockPublisher, "")
		assert.NotNil(t, usecase)
	})
}

func TestDeleteRatingMessage_Structure(t *testing.T) {
	t.Run("json_marshaling", func(t *testing.T) {
		message := uc.DeleteRatingMessage{
			EventID:       "test-event-id",
			Action:        "delete_rating",
			CorrelationID: "test-correlation-id",
		}

		data, err := json.Marshal(message)
		assert.NoError(t, err)
		assert.NotEmpty(t, data)

		var unmarshaled uc.DeleteRatingMessage
		err = json.Unmarshal(data, &unmarshaled)
		assert.NoError(t, err)
		assert.Equal(t, message.EventID, unmarshaled.EventID)
		assert.Equal(t, message.Action, unmarshaled.Action)
		assert.Equal(t, message.CorrelationID, unmarshaled.CorrelationID)
	})
}
