package usecase_test

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/repository"
	uc "gitlab.viswalslab.com/backend/ratings/internal/rating/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
	"go.uber.org/mock/gomock"
)

// Mock repository for testing delete rating
type mockDeleteRatingRepository struct {
	ctrl     *gomock.Controller
	recorder *MockDeleteRatingRepositoryMockRecorder
}

type MockDeleteRatingRepositoryMockRecorder struct {
	mock *mockDeleteRatingRepository
}

func NewMockDeleteRatingRepository(ctrl *gomock.Controller) *mockDeleteRatingRepository {
	mock := &mockDeleteRatingRepository{ctrl: ctrl}
	mock.recorder = &MockDeleteRatingRepositoryMockRecorder{mock}
	return mock
}

func (m *mockDeleteRatingRepository) EXPECT() *MockDeleteRatingRepositoryMockRecorder {
	return m.recorder
}

func (m *mockDeleteRatingRepository) DeleteRatingByEvent(ctx context.Context, eventID string) (*repository.DeleteRatingByEventResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRatingByEvent", ctx, eventID)
	ret0, _ := ret[0].(*repository.DeleteRatingByEventResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockDeleteRatingRepositoryMockRecorder) DeleteRatingByEvent(ctx, eventID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRatingByEvent", reflect.TypeOf((*mockDeleteRatingRepository)(nil).DeleteRatingByEvent), ctx, eventID)
}

func TestDeleteRatingUsecase_Execute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	mockRepo := NewMockDeleteRatingRepository(ctrl)
	u := uc.NewDeleteRatingUsecase(mockRepo)

	t.Run("success_single_record_deleted", func(t *testing.T) {
		input := &uc.DeleteRatingInput{
			EventID: "test-event-id",
		}

		// Mock successful deletion
		mockRepo.EXPECT().DeleteRatingByEvent(ctx, input.EventID).Return(&repository.DeleteRatingByEventResult{
			DeletedCount: 1,
			EventID:      input.EventID,
		}, nil)

		output, err := u.Execute(ctx, input)
		assert.NoError(t, err)
		assert.NotNil(t, output)
		assert.True(t, output.Success)
		assert.Equal(t, "Rating deleted successfully", output.Message)
		assert.Equal(t, input.EventID, output.EventID)
		assert.Equal(t, int64(1), output.DeletedCount)
	})

	t.Run("success_no_records_found", func(t *testing.T) {
		input := &uc.DeleteRatingInput{
			EventID: "non-existent-event-id",
		}

		// Mock deletion with no records found
		mockRepo.EXPECT().DeleteRatingByEvent(ctx, input.EventID).Return(&repository.DeleteRatingByEventResult{
			DeletedCount: 0,
			EventID:      input.EventID,
		}, nil)

		output, err := u.Execute(ctx, input)
		assert.NoError(t, err)
		assert.NotNil(t, output)
		assert.True(t, output.Success)
		assert.Equal(t, "No ratings found for the given event_id", output.Message)
		assert.Equal(t, input.EventID, output.EventID)
		assert.Equal(t, int64(0), output.DeletedCount)
	})

	t.Run("repository_error", func(t *testing.T) {
		input := &uc.DeleteRatingInput{
			EventID: "test-event-id",
		}

		// Mock repository error
		repoError := errors.New("database connection failed")
		mockRepo.EXPECT().DeleteRatingByEvent(ctx, input.EventID).Return(nil, repoError)

		output, err := u.Execute(ctx, input)
		assert.Error(t, err)
		assert.Nil(t, output)
		assert.Equal(t, repoError, err)
	})

	t.Run("empty_event_id", func(t *testing.T) {
		input := &uc.DeleteRatingInput{
			EventID: "",
		}

		// No mock expectation since validation should fail before repository call
		output, err := u.Execute(ctx, input)
		assert.Error(t, err)
		assert.Nil(t, output)
		assert.Equal(t, uc.ErrEventIDRequired, err)
	})

	t.Run("success_multiple_records_deleted", func(t *testing.T) {
		input := &uc.DeleteRatingInput{
			EventID: "test-event-id-multiple",
		}

		// Mock deletion with multiple records
		mockRepo.EXPECT().DeleteRatingByEvent(ctx, input.EventID).Return(&repository.DeleteRatingByEventResult{
			DeletedCount: 3,
			EventID:      input.EventID,
		}, nil)

		output, err := u.Execute(ctx, input)
		assert.NoError(t, err)
		assert.NotNil(t, output)
		assert.True(t, output.Success)
		assert.Equal(t, "Rating deleted successfully", output.Message)
		assert.Equal(t, input.EventID, output.EventID)
		assert.Equal(t, int64(3), output.DeletedCount)
	})
}

func TestNewDeleteRatingUsecase(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockDeleteRatingRepository(ctrl)

	t.Run("success", func(t *testing.T) {
		usecase := uc.NewDeleteRatingUsecase(mockRepo)
		assert.NotNil(t, usecase)
	})

	t.Run("with_nil_repository", func(t *testing.T) {
		usecase := uc.NewDeleteRatingUsecase(nil)
		assert.NotNil(t, usecase)
	})
}
