package usecase

import (
	"context"
	"errors"

	"gitlab.viswalslab.com/backend/ratings/internal/rating/repository"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var deleteRatingUsecaseName = "DeleteRatingUsecase"

// ErrEventIDRequired is returned when event_id is empty
var ErrEventIDRequired = errors.New("event_id is required")

type deleteRatingRepository interface {
	DeleteRatingByEvent(ctx context.Context, eventID string) (*repository.DeleteRatingByEventResult, error)
}

type DeleteRatingUsecase struct {
	repo deleteRatingRepository
}

func NewDeleteRatingUsecase(repo deleteRatingRepository) *DeleteRatingUsecase {
	return &DeleteRatingUsecase{
		repo: repo,
	}
}

type DeleteRatingInput struct {
	EventID string `json:"event_id"`
}

type DeleteRatingOutput struct {
	Success      bool   `json:"success"`
	Message      string `json:"message"`
	EventID      string `json:"event_id"`
	DeletedCount int64  `json:"deleted_count"`
}

func (uc *DeleteRatingUsecase) Execute(ctx context.Context, input *DeleteRatingInput) (*DeleteRatingOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", deleteRatingUsecaseName))
	logger.Debug("trying to execute delete rating", vlog.F("input", input))

	// Validate input
	if input.EventID == "" {
		logger.Error("event_id is required for deletion")
		return nil, ErrEventIDRequired
	}

	// Delete rating from database
	result, err := uc.repo.DeleteRatingByEvent(ctx, input.EventID)
	if err != nil {
		logger.Error("failed to delete rating",
			vlog.F("error", err),
			vlog.F("event_id", input.EventID))
		return nil, err
	}

	logger.Info("rating deleted successfully",
		vlog.F("event_id", input.EventID),
		vlog.F("deleted_count", result.DeletedCount))

	message := "Rating deleted successfully"
	if result.DeletedCount == 0 {
		message = "No ratings found for the given event_id"
	}

	return &DeleteRatingOutput{
		Success:      true,
		Message:      message,
		EventID:      input.EventID,
		DeletedCount: result.DeletedCount,
	}, nil
}
