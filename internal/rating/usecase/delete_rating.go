package usecase

import (
	"context"
	"encoding/json"
	"time"

	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.viswalslab.com/backend/ratings/core"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var deleteRatingUsecaseName = "DeleteRatingUsecase"

type deleteRatingPublisher interface {
	Publish(ctx context.Context, exchangeName string, key string, publishing amqp.Publishing) error
}

type DeleteRatingUsecase struct {
	rabbitmq  deleteRatingPublisher
	queueName string
}

func NewDeleteRatingUsecase(rabbitmq deleteRatingPublisher, queueName string) *DeleteRatingUsecase {
	return &DeleteRatingUsecase{
		rabbitmq:  rabbitmq,
		queueName: queueName,
	}
}

type DeleteRatingInput struct {
	EventID string `json:"event_id"`
}

type DeleteRatingOutput struct {
	Success       bool   `json:"success"`
	CorrelationID string `json:"correlation_id"`
	Message       string `json:"message"`
	EventID       string `json:"event_id"`
}

type DeleteRatingMessage struct {
	EventID       string    `json:"event_id"`
	Action        string    `json:"action"`
	CorrelationID string    `json:"correlation_id"`
	RequestedAt   time.Time `json:"requested_at"`
}

func (uc *DeleteRatingUsecase) Execute(ctx context.Context, input *DeleteRatingInput) (*DeleteRatingOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", deleteRatingUsecaseName))
	logger.Debug("trying to execute delete rating", vlog.F("input", input))

	// Generate a correlation ID for this request
	correlationID := core.NewID().Value()

	// Create the delete message payload for RabbitMQ
	deleteMessage := DeleteRatingMessage{
		EventID:       input.EventID,
		Action:        "delete_rating",
		CorrelationID: correlationID,
		RequestedAt:   time.Now(),
	}

	// Marshal the delete message to JSON
	messageBody, err := json.Marshal(deleteMessage)
	if err != nil {
		logger.Error("failed to marshal delete message", vlog.F("error", err))
		return nil, err
	}

	// Create the AMQP publishing message
	publishing := amqp.Publishing{
		ContentType:   "application/json",
		Body:          messageBody,
		CorrelationId: correlationID,
		Timestamp:     time.Now(),
	}

	// Publish to RabbitMQ queue (using empty exchange name for direct queue publishing)
	err = uc.rabbitmq.Publish(ctx, "", uc.queueName, publishing)
	if err != nil {
		logger.Error("failed to publish delete rating to rabbitmq", 
			vlog.F("error", err), 
			vlog.F("correlation_id", correlationID),
			vlog.F("event_id", input.EventID))
		return nil, err
	}

	logger.Info("delete rating request published successfully", 
		vlog.F("correlation_id", correlationID),
		vlog.F("event_id", input.EventID))

	return &DeleteRatingOutput{
		Success:       true,
		CorrelationID: correlationID,
		Message:       "Delete rating request submitted successfully",
		EventID:       input.EventID,
	}, nil
}
