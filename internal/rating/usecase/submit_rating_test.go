package usecase_test

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"testing"

	amqp "github.com/rabbitmq/amqp091-go"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/repository"
	uc "gitlab.viswalslab.com/backend/ratings/internal/rating/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
	"go.uber.org/mock/gomock"
)

// Mock RabbitMQ publisher for testing
type mockRabbitmqPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockRabbitmqPublisherMockRecorder
}

type MockRabbitmqPublisherMockRecorder struct {
	mock *mockRabbitmqPublisher
}

func NewMockRabbitmqPublisher(ctrl *gomock.Controller) *mockRabbitmqPublisher {
	mock := &mockRabbitmqPublisher{ctrl: ctrl}
	mock.recorder = &MockRabbitmqPublisherMockRecorder{mock}
	return mock
}

func (m *mockRabbitmqPublisher) EXPECT() *MockRabbitmqPublisherMockRecorder {
	return m.recorder
}

func (m *mockRabbitmqPublisher) Publish(ctx context.Context, exchangeName string, key string, publishing amqp.Publishing) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Publish", ctx, exchangeName, key, publishing)
	ret0, _ := ret[0].(error)
	return ret0
}

func (mr *MockRabbitmqPublisherMockRecorder) Publish(ctx, exchangeName, key, publishing interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*mockRabbitmqPublisher)(nil).Publish), ctx, exchangeName, key, publishing)
}

func TestSubmitRatingUsecase_Execute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	mockPublisher := NewMockRabbitmqPublisher(ctrl)
	queueName := "test-rating-queue"
	u := uc.NewSubmitRatingUsecase(mockPublisher, queueName)

	t.Run("success", func(t *testing.T) {
		input := &uc.SubmitRatingInput{
			EventID:            "test-event-id",
			FromRole:           "user",
			FromEntityID:       "test-from-entity",
			ToRole:             "pro",
			ToEntityID:         "test-to-entity",
			FieldTag:           "service",
			StarRating:         5,
			OverallExperience:  "Excellent service",
			ProfessionalSkills: "Great technical skills",
			PersonalSkills:     "Amazing personality",
			EngagementSkills:   "Outstanding communication",
			ThankingNote:       "Thank you very much",
			FormData:           map[string]interface{}{"key": "value", "rating": 5},
		}

		// Mock successful publish
		mockPublisher.EXPECT().Publish(
			gomock.Any(),
			"",
			queueName,
			gomock.Any(),
		).Return(nil)

		output, err := u.Execute(ctx, input)
		assert.NoError(t, err)
		assert.NotNil(t, output)
		assert.True(t, output.Success)
		assert.NotEmpty(t, output.CorrelationID)
		assert.Equal(t, "Rating submitted successfully", output.Message)
	})

	t.Run("success_with_minimal_input", func(t *testing.T) {
		input := &uc.SubmitRatingInput{
			EventID:      "test-event-id",
			FromRole:     "user",
			FromEntityID: "test-from-entity",
			ToRole:       "pro",
			ToEntityID:   "test-to-entity",
			StarRating:   3,
		}

		// Mock successful publish
		mockPublisher.EXPECT().Publish(
			gomock.Any(),
			"",
			queueName,
			gomock.Any(),
		).Return(nil)

		output, err := u.Execute(ctx, input)
		assert.NoError(t, err)
		assert.NotNil(t, output)
		assert.True(t, output.Success)
		assert.NotEmpty(t, output.CorrelationID)
		assert.Equal(t, "Rating submitted successfully", output.Message)
	})

	t.Run("rabbitmq_publish_error", func(t *testing.T) {
		input := &uc.SubmitRatingInput{
			EventID:      "test-event-id",
			FromRole:     "user",
			FromEntityID: "test-from-entity",
			ToRole:       "pro",
			ToEntityID:   "test-to-entity",
			StarRating:   4,
		}

		publishError := errors.New("rabbitmq connection failed")
		mockPublisher.EXPECT().Publish(
			gomock.Any(),
			"",
			queueName,
			gomock.Any(),
		).Return(publishError)

		output, err := u.Execute(ctx, input)
		assert.Error(t, err)
		assert.Nil(t, output)
		assert.Equal(t, publishError, err)
	})

	t.Run("verify_message_structure", func(t *testing.T) {
		input := &uc.SubmitRatingInput{
			EventID:            "test-event-id",
			FromRole:           "user",
			FromEntityID:       "test-from-entity",
			ToRole:             "pro",
			ToEntityID:         "test-to-entity",
			FieldTag:           "service",
			StarRating:         5,
			OverallExperience:  "Excellent",
			ProfessionalSkills: "Great",
			PersonalSkills:     "Amazing",
			EngagementSkills:   "Outstanding",
			ThankingNote:       "Thank you",
			FormData:           map[string]interface{}{"key": "value"},
		}

		// Capture the published message
		var capturedPublishing amqp.Publishing
		mockPublisher.EXPECT().Publish(
			gomock.Any(),
			"",
			queueName,
			gomock.Any(),
		).DoAndReturn(func(ctx context.Context, exchangeName string, key string, publishing amqp.Publishing) error {
			capturedPublishing = publishing
			return nil
		})

		output, err := u.Execute(ctx, input)
		assert.NoError(t, err)
		assert.NotNil(t, output)

		// Verify message structure
		assert.Equal(t, "application/json", capturedPublishing.ContentType)
		assert.NotEmpty(t, capturedPublishing.CorrelationId)
		assert.NotZero(t, capturedPublishing.Timestamp)

		// Unmarshal and verify the message body
		var logbook repository.Logbooks
		err = json.Unmarshal(capturedPublishing.Body, &logbook)
		assert.NoError(t, err)
		assert.Equal(t, input.EventID, logbook.EventID)
		assert.Equal(t, input.FromRole, logbook.FromRole)
		assert.Equal(t, input.FromEntityID, logbook.FromEntityID)
		assert.Equal(t, input.ToRole, logbook.ToRole)
		assert.Equal(t, input.ToEntityID, logbook.ToEntityID)
		assert.Equal(t, input.FieldTag, logbook.FieldTag)
		assert.Equal(t, input.StarRating, logbook.StarRating)
		assert.Equal(t, input.OverallExperience, logbook.OverallExperience)
		assert.Equal(t, input.ProfessionalSkills, logbook.ProfessionalSkills)
		assert.Equal(t, input.PersonalSkills, logbook.PersonalSkills)
		assert.Equal(t, input.EngagementSkills, logbook.EngagementSkills)
		assert.Equal(t, input.ThankingNote, logbook.ThankingNote)
		assert.Equal(t, input.FormData, logbook.FormData)
		assert.Equal(t, output.CorrelationID, logbook.CorrelationID)
		assert.NotZero(t, logbook.SubmittedAt)
	})

	t.Run("success_with_nil_form_data", func(t *testing.T) {
		input := &uc.SubmitRatingInput{
			EventID:      "test-event-id",
			FromRole:     "user",
			FromEntityID: "test-from-entity",
			ToRole:       "pro",
			ToEntityID:   "test-to-entity",
			StarRating:   4,
			FormData:     nil,
		}

		mockPublisher.EXPECT().Publish(
			gomock.Any(),
			"",
			queueName,
			gomock.Any(),
		).Return(nil)

		output, err := u.Execute(ctx, input)
		assert.NoError(t, err)
		assert.NotNil(t, output)
		assert.True(t, output.Success)
	})

	t.Run("success_with_empty_form_data", func(t *testing.T) {
		input := &uc.SubmitRatingInput{
			EventID:      "test-event-id",
			FromRole:     "user",
			FromEntityID: "test-from-entity",
			ToRole:       "pro",
			ToEntityID:   "test-to-entity",
			StarRating:   2,
			FormData:     map[string]interface{}{},
		}

		mockPublisher.EXPECT().Publish(
			gomock.Any(),
			"",
			queueName,
			gomock.Any(),
		).Return(nil)

		output, err := u.Execute(ctx, input)
		assert.NoError(t, err)
		assert.NotNil(t, output)
		assert.True(t, output.Success)
	})

	t.Run("verify_correlation_id_uniqueness", func(t *testing.T) {
		input := &uc.SubmitRatingInput{
			EventID:      "test-event-id",
			FromRole:     "user",
			FromEntityID: "test-from-entity",
			ToRole:       "pro",
			ToEntityID:   "test-to-entity",
			StarRating:   5,
		}

		var correlationIDs []string
		mockPublisher.EXPECT().Publish(
			gomock.Any(),
			"",
			queueName,
			gomock.Any(),
		).DoAndReturn(func(ctx context.Context, exchangeName string, key string, publishing amqp.Publishing) error {
			correlationIDs = append(correlationIDs, publishing.CorrelationId)
			return nil
		}).Times(3)

		// Execute multiple times to verify unique correlation IDs
		for i := 0; i < 3; i++ {
			output, err := u.Execute(ctx, input)
			assert.NoError(t, err)
			assert.NotNil(t, output)
		}

		// Verify all correlation IDs are unique
		assert.Len(t, correlationIDs, 3)
		assert.NotEqual(t, correlationIDs[0], correlationIDs[1])
		assert.NotEqual(t, correlationIDs[1], correlationIDs[2])
		assert.NotEqual(t, correlationIDs[0], correlationIDs[2])
	})
}

func TestNewSubmitRatingUsecase(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockPublisher := NewMockRabbitmqPublisher(ctrl)
	queueName := "test-rating-queue"

	t.Run("success", func(t *testing.T) {
		usecase := uc.NewSubmitRatingUsecase(mockPublisher, queueName)
		assert.NotNil(t, usecase)
	})

	t.Run("with_nil_publisher", func(t *testing.T) {
		usecase := uc.NewSubmitRatingUsecase(nil, queueName)
		assert.NotNil(t, usecase)
	})

	t.Run("with_empty_queue_name", func(t *testing.T) {
		usecase := uc.NewSubmitRatingUsecase(mockPublisher, "")
		assert.NotNil(t, usecase)
	})
}
