package repository

import (
	"context"
	"database/sql"

	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type DeleteRatingByEventResult struct {
	DeletedCount int64  `json:"deleted_count"`
	EventID      string `json:"event_id"`
}

func (r *ratingRepo) DeleteRatingByEvent(ctx context.Context, eventID string) (*DeleteRatingByEventResult, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "deleteRatingByEvent"), vlog.F("action", "delete rating by event_id"))
	logger.Debug("trying to delete rating by event_id", vlog.F("event_id", eventID))

	if eventID == "" {
		logger.Error("event_id is required for deletion")
		return nil, sql.ErrNoRows
	}

	// Begin a transaction
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction", vlog.F("error", err))
		return nil, err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Delete from log_books table by event_id
	query := `DELETE FROM log_books WHERE event_id = $1`
	result, err := tx.ExecContext(ctx, query, eventID)
	if err != nil {
		logger.Error("failed to delete rating by event_id", vlog.F("error", err), vlog.F("event_id", eventID))
		return nil, err
	}

	// Get the number of affected rows
	deletedCount, err := result.RowsAffected()
	if err != nil {
		logger.Error("failed to get rows affected", vlog.F("error", err))
		return nil, err
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		logger.Error("failed to commit transaction", vlog.F("error", err))
		return nil, err
	}

	logger.Info("rating deleted successfully", 
		vlog.F("event_id", eventID), 
		vlog.F("deleted_count", deletedCount))

	return &DeleteRatingByEventResult{
		DeletedCount: deletedCount,
		EventID:      eventID,
	}, nil
}

func DeleteRatingByEventWithDB(ctx context.Context, db *sql.DB, eventID string) (*DeleteRatingByEventResult, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("function", "DeleteRatingByEventWithDB"))
	logger.Debug("trying to delete rating by event_id", vlog.F("event_id", eventID))

	if eventID == "" {
		logger.Error("event_id is required for deletion")
		return nil, sql.ErrNoRows
	}

	// Begin a transaction
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction", vlog.F("error", err))
		return nil, err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Delete from log_books table by event_id
	query := `DELETE FROM log_books WHERE event_id = $1`
	result, err := tx.ExecContext(ctx, query, eventID)
	if err != nil {
		logger.Error("failed to delete rating by event_id", vlog.F("error", err), vlog.F("event_id", eventID))
		return nil, err
	}

	// Get the number of affected rows
	deletedCount, err := result.RowsAffected()
	if err != nil {
		logger.Error("failed to get rows affected", vlog.F("error", err))
		return nil, err
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		logger.Error("failed to commit transaction", vlog.F("error", err))
		return nil, err
	}

	logger.Info("rating deleted successfully", 
		vlog.F("event_id", eventID), 
		vlog.F("deleted_count", deletedCount))

	return &DeleteRatingByEventResult{
		DeletedCount: deletedCount,
		EventID:      eventID,
	}, nil
}
