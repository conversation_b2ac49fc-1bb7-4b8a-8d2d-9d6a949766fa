package repository

import (
	"context"
	"database/sql"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func TestRatingRepo_GetRatingOptions_Success(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")
	repo := NewRatingRepo(sqlxDB)

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	t.Run("success_with_all_filters", func(t *testing.T) {
		filter := &GetRatingOptionsFilter{
			OptionType:        stringPtrDB("overall_experience"),
			ApplicableToRoles: []string{"user", "pro"},
			MinStars:          4,
			FieldTag:          stringPtrDB("service"),
		}

		expectedQuery := `SELECT id, option_type, option_value, applicable_to_roles, min_stars, field_tag, display_order
			FROM rating_options
			WHERE 1 = 1 AND option_type = \$1 AND applicable_to_roles && \$2 AND min_stars = \$3 AND \(field_tag IS NULL OR field_tag = \$4\) ORDER BY display_order ASC`

		rows := sqlmock.NewRows([]string{"id", "option_type", "option_value", "applicable_to_roles", "min_stars", "field_tag", "display_order"}).
			AddRow(1, "overall_experience", "Excellent service", pq.Array([]string{"user", "pro"}), 4, "service", 1).
			AddRow(2, "overall_experience", "Good service", pq.Array([]string{"user", "pro"}), 4, "service", 2)

		mock.ExpectQuery(expectedQuery).
			WithArgs("overall_experience", pq.Array([]string{"user", "pro"}), 4, "service").
			WillReturnRows(rows)

		result, err := repo.GetRatingOptions(ctx, filter)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.Options, 2)
		assert.Equal(t, 1, result.Options[0].ID)
		assert.Equal(t, "overall_experience", result.Options[0].OptionType)
		assert.Equal(t, "Excellent service", result.Options[0].Value)
		assert.Equal(t, pq.StringArray{"user", "pro"}, result.Options[0].ApplicableToRoles)
		assert.Equal(t, 4, *result.Options[0].MinStars)
		assert.Equal(t, "service", *result.Options[0].FieldTag)
		assert.Equal(t, 1, result.Options[0].DisplayOrder)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_with_minimal_filters", func(t *testing.T) {
		filter := &GetRatingOptionsFilter{
			ApplicableToRoles: []string{"user"},
			MinStars:          3,
		}

		expectedQuery := `SELECT id, option_type, option_value, applicable_to_roles, min_stars, field_tag, display_order
			FROM rating_options
			WHERE 1 = 1 AND applicable_to_roles && \$1 AND min_stars = \$2 ORDER BY display_order ASC`

		rows := sqlmock.NewRows([]string{"id", "option_type", "option_value", "applicable_to_roles", "min_stars", "field_tag", "display_order"}).
			AddRow(3, "professional_skills", "Great skills", pq.Array([]string{"user"}), 3, nil, 3)

		mock.ExpectQuery(expectedQuery).
			WithArgs(pq.Array([]string{"user"}), 3).
			WillReturnRows(rows)

		result, err := repo.GetRatingOptions(ctx, filter)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.Options, 1)
		assert.Equal(t, 3, result.Options[0].ID)
		assert.Equal(t, "professional_skills", result.Options[0].OptionType)
		assert.Nil(t, result.Options[0].FieldTag)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_with_nil_filter", func(t *testing.T) {
		expectedQuery := `SELECT id, option_type, option_value, applicable_to_roles, min_stars, field_tag, display_order
			FROM rating_options
			WHERE 1 = 1 ORDER BY display_order ASC`

		rows := sqlmock.NewRows([]string{"id", "option_type", "option_value", "applicable_to_roles", "min_stars", "field_tag", "display_order"}).
			AddRow(1, "overall_experience", "Excellent", pq.Array([]string{"user"}), 5, nil, 1)

		mock.ExpectQuery(expectedQuery).
			WillReturnRows(rows)

		result, err := repo.GetRatingOptions(ctx, nil)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.Options, 1)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_empty_results", func(t *testing.T) {
		filter := &GetRatingOptionsFilter{
			OptionType: stringPtrDB("non_existent"),
			MinStars:   5,
		}

		expectedQuery := `SELECT id, option_type, option_value, applicable_to_roles, min_stars, field_tag, display_order
			FROM rating_options
			WHERE 1 = 1 AND option_type = \$1 AND min_stars = \$2 ORDER BY display_order ASC`

		rows := sqlmock.NewRows([]string{"id", "option_type", "option_value", "applicable_to_roles", "min_stars", "field_tag", "display_order"})

		mock.ExpectQuery(expectedQuery).
			WithArgs("non_existent", 5).
			WillReturnRows(rows)

		result, err := repo.GetRatingOptions(ctx, filter)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.Options, 0)

		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestRatingRepo_GetRatingOptions_Errors(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")
	repo := NewRatingRepo(sqlxDB)

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	t.Run("query_error", func(t *testing.T) {
		filter := &GetRatingOptionsFilter{
			MinStars: 3,
		}

		expectedQuery := `SELECT id, option_type, option_value, applicable_to_roles, min_stars, field_tag, display_order
			FROM rating_options
			WHERE 1 = 1 AND min_stars = \$1 ORDER BY display_order ASC`

		mock.ExpectQuery(expectedQuery).
			WithArgs(3).
			WillReturnError(sql.ErrConnDone)

		result, err := repo.GetRatingOptions(ctx, filter)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, sql.ErrConnDone, err)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("scan_error", func(t *testing.T) {
		filter := &GetRatingOptionsFilter{
			MinStars: 3,
		}

		expectedQuery := `SELECT id, option_type, option_value, applicable_to_roles, min_stars, field_tag, display_order
			FROM rating_options
			WHERE 1 = 1 AND min_stars = \$1 ORDER BY display_order ASC`

		// Create rows with invalid data type to cause scan error
		rows := sqlmock.NewRows([]string{"id", "option_type", "option_value", "applicable_to_roles", "min_stars", "field_tag", "display_order"}).
			AddRow("invalid_id", "overall_experience", "Excellent", pq.Array([]string{"user"}), 3, nil, 1)

		mock.ExpectQuery(expectedQuery).
			WithArgs(3).
			WillReturnRows(rows)

		result, err := repo.GetRatingOptions(ctx, filter)
		assert.Error(t, err)
		assert.Nil(t, result)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("rows_error", func(t *testing.T) {
		filter := &GetRatingOptionsFilter{
			MinStars: 3,
		}

		expectedQuery := `SELECT id, option_type, option_value, applicable_to_roles, min_stars, field_tag, display_order
			FROM rating_options
			WHERE 1 = 1 AND min_stars = \$1 ORDER BY display_order ASC`

		rows := sqlmock.NewRows([]string{"id", "option_type", "option_value", "applicable_to_roles", "min_stars", "field_tag", "display_order"}).
			AddRow(1, "overall_experience", "Excellent", pq.Array([]string{"user"}), 3, nil, 1).
			RowError(0, sql.ErrTxDone)

		mock.ExpectQuery(expectedQuery).
			WithArgs(3).
			WillReturnRows(rows)

		result, err := repo.GetRatingOptions(ctx, filter)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, sql.ErrTxDone, err)

		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestRatingRepo_GetRatingOptions_FilterCombinations(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")
	repo := NewRatingRepo(sqlxDB)

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	t.Run("only_option_type_and_min_stars", func(t *testing.T) {
		filter := &GetRatingOptionsFilter{
			OptionType: stringPtrDB("personal_skills"),
			MinStars:   2,
		}

		expectedQuery := `SELECT id, option_type, option_value, applicable_to_roles, min_stars, field_tag, display_order
			FROM rating_options
			WHERE 1 = 1 AND option_type = \$1 AND min_stars = \$2 ORDER BY display_order ASC`

		rows := sqlmock.NewRows([]string{"id", "option_type", "option_value", "applicable_to_roles", "min_stars", "field_tag", "display_order"}).
			AddRow(4, "personal_skills", "Friendly", pq.Array([]string{"user", "pro"}), 2, nil, 4)

		mock.ExpectQuery(expectedQuery).
			WithArgs("personal_skills", 2).
			WillReturnRows(rows)

		result, err := repo.GetRatingOptions(ctx, filter)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.Options, 1)
		assert.Equal(t, "personal_skills", result.Options[0].OptionType)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("only_field_tag_and_min_stars", func(t *testing.T) {
		filter := &GetRatingOptionsFilter{
			FieldTag: stringPtrDB("communication"),
			MinStars: 1,
		}

		expectedQuery := `SELECT id, option_type, option_value, applicable_to_roles, min_stars, field_tag, display_order
			FROM rating_options
			WHERE 1 = 1 AND min_stars = \$1 AND \(field_tag IS NULL OR field_tag = \$2\) ORDER BY display_order ASC`

		rows := sqlmock.NewRows([]string{"id", "option_type", "option_value", "applicable_to_roles", "min_stars", "field_tag", "display_order"}).
			AddRow(5, "engagement_skills", "Great communication", pq.Array([]string{"pro"}), 1, "communication", 5)

		mock.ExpectQuery(expectedQuery).
			WithArgs(1, "communication").
			WillReturnRows(rows)

		result, err := repo.GetRatingOptions(ctx, filter)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.Options, 1)
		assert.Equal(t, "communication", *result.Options[0].FieldTag)

		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

// Helper functions for the new tests
func stringPtrDB(s string) *string {
	return &s
}
