package repository

import (
	"context"
	"database/sql"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func TestRatingRepo_DeleteRatingByEvent_Success(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")
	repo := NewRatingRepo(sqlxDB)

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	t.Run("success_single_record_deleted", func(t *testing.T) {
		eventID := "test-event-id"

		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM log_books WHERE event_id = \$1`).
			WithArgs(eventID).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		result, err := repo.DeleteRatingByEvent(ctx, eventID)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, int64(1), result.DeletedCount)
		assert.Equal(t, eventID, result.EventID)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_multiple_records_deleted", func(t *testing.T) {
		eventID := "test-event-id-multiple"

		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM log_books WHERE event_id = \$1`).
			WithArgs(eventID).
			WillReturnResult(sqlmock.NewResult(0, 3))
		mock.ExpectCommit()

		result, err := repo.DeleteRatingByEvent(ctx, eventID)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, int64(3), result.DeletedCount)
		assert.Equal(t, eventID, result.EventID)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_no_records_found", func(t *testing.T) {
		eventID := "non-existent-event-id"

		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM log_books WHERE event_id = \$1`).
			WithArgs(eventID).
			WillReturnResult(sqlmock.NewResult(0, 0))
		mock.ExpectCommit()

		result, err := repo.DeleteRatingByEvent(ctx, eventID)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, int64(0), result.DeletedCount)
		assert.Equal(t, eventID, result.EventID)

		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestRatingRepo_DeleteRatingByEvent_Errors(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")
	repo := NewRatingRepo(sqlxDB)

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	t.Run("empty_event_id", func(t *testing.T) {
		result, err := repo.DeleteRatingByEvent(ctx, "")
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, sql.ErrNoRows, err)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("begin_transaction_error", func(t *testing.T) {
		eventID := "test-event-id"

		mock.ExpectBegin().WillReturnError(sql.ErrConnDone)

		result, err := repo.DeleteRatingByEvent(ctx, eventID)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, sql.ErrConnDone, err)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("delete_query_error", func(t *testing.T) {
		eventID := "test-event-id"

		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM log_books WHERE event_id = \$1`).
			WithArgs(eventID).
			WillReturnError(sql.ErrTxDone)
		mock.ExpectRollback()

		result, err := repo.DeleteRatingByEvent(ctx, eventID)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, sql.ErrTxDone, err)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("rows_affected_error", func(t *testing.T) {
		eventID := "test-event-id"

		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM log_books WHERE event_id = \$1`).
			WithArgs(eventID).
			WillReturnResult(sqlmock.NewErrorResult(sql.ErrNoRows))
		mock.ExpectRollback()

		result, err := repo.DeleteRatingByEvent(ctx, eventID)
		assert.Error(t, err)
		assert.Nil(t, result)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("commit_error", func(t *testing.T) {
		eventID := "test-event-id"

		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM log_books WHERE event_id = \$1`).
			WithArgs(eventID).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit().WillReturnError(sql.ErrTxDone)

		result, err := repo.DeleteRatingByEvent(ctx, eventID)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, sql.ErrTxDone, err)

		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteRatingByEventWithDB_Success(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	t.Run("success_single_record_deleted", func(t *testing.T) {
		eventID := "test-event-id"

		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM log_books WHERE event_id = \$1`).
			WithArgs(eventID).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		result, err := DeleteRatingByEventWithDB(ctx, db, eventID)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, int64(1), result.DeletedCount)
		assert.Equal(t, eventID, result.EventID)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_no_records_found", func(t *testing.T) {
		eventID := "non-existent-event-id"

		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM log_books WHERE event_id = \$1`).
			WithArgs(eventID).
			WillReturnResult(sqlmock.NewResult(0, 0))
		mock.ExpectCommit()

		result, err := DeleteRatingByEventWithDB(ctx, db, eventID)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, int64(0), result.DeletedCount)
		assert.Equal(t, eventID, result.EventID)

		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDeleteRatingByEventWithDB_Errors(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	logger := vlog.New()
	ctx := vlog.AttachLoggerToContext(context.Background(), logger)

	t.Run("empty_event_id", func(t *testing.T) {
		result, err := DeleteRatingByEventWithDB(ctx, db, "")
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, sql.ErrNoRows, err)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("begin_transaction_error", func(t *testing.T) {
		eventID := "test-event-id"

		mock.ExpectBegin().WillReturnError(sql.ErrConnDone)

		result, err := DeleteRatingByEventWithDB(ctx, db, eventID)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, sql.ErrConnDone, err)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("delete_query_error", func(t *testing.T) {
		eventID := "test-event-id"

		mock.ExpectBegin()
		mock.ExpectExec(`DELETE FROM log_books WHERE event_id = \$1`).
			WithArgs(eventID).
			WillReturnError(sql.ErrTxDone)
		mock.ExpectRollback()

		result, err := DeleteRatingByEventWithDB(ctx, db, eventID)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, sql.ErrTxDone, err)

		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
