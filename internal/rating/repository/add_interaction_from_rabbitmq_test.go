package repository_test

import (
	"context"
	"database/sql"
	"encoding/json"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/repository"
)

func TestInsertlogbooksWithLogBook_Success_GeneratedUUID(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	ctx := context.Background()
	logbooks := &repository.Logbooks{
		ID:                 "",
		EventID:            "test-event-id",
		FromRole:           "user",
		FromEntityID:       "test-from-entity",
		ToRole:             "pro",
		ToEntityID:         "test-to-entity",
		FieldTag:           "service",
		StarRating:         5,
		OverallExperience:  "Excellent",
		EngagementSkills:   "Outstanding",
		ProfessionalSkills: "Great",
		PersonalSkills:     "Amazing",
		ThankingNote:       "Thank you",
		FormData:           map[string]interface{}{"key": "value", "rating": 5},
		SubmittedAt:        time.Now(),
		CorrelationID:      "test-correlation-id",
	}

	mock.ExpectBegin()

	mock.ExpectExec(`INSERT INTO log_books`).
		WithArgs(
			sqlmock.AnyArg(), // ID (generated UUID)
			logbooks.EventID,
			logbooks.FromRole,
			logbooks.FromEntityID,
			logbooks.ToRole,
			logbooks.ToEntityID,
			logbooks.FieldTag,
			logbooks.StarRating,
			logbooks.OverallExperience,
			logbooks.ProfessionalSkills,
			logbooks.PersonalSkills,
			logbooks.ThankingNote,
			sqlmock.AnyArg(),
			sqlmock.AnyArg(),
			logbooks.CorrelationID,
			logbooks.EngagementSkills,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	mock.ExpectCommit()

	err = repository.InsertlogbooksWithLogBook(ctx, db, logbooks)

	assert.NoError(t, err)
	assert.NotEmpty(t, logbooks.ID, "ID should be generated")

	_, err = uuid.Parse(logbooks.ID)
	assert.NoError(t, err, "Generated ID should be a valid UUID")

	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestInsertlogbooksWithLogBook_Success_ProvidedID(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	ctx := context.Background()
	providedID := uuid.NewString()
	logbooks := &repository.Logbooks{
		ID:                 providedID,
		EventID:            "test-event-id",
		FromRole:           "user",
		FromEntityID:       "test-from-entity",
		ToRole:             "pro",
		ToEntityID:         "test-to-entity",
		FieldTag:           "service",
		StarRating:         5,
		OverallExperience:  "Excellent",
		EngagementSkills:   "Outstanding",
		ProfessionalSkills: "Great",
		PersonalSkills:     "Amazing",
		ThankingNote:       "Thank you",
		FormData:           map[string]interface{}{"key": "value"},
		SubmittedAt:        time.Now(),
		CorrelationID:      "test-correlation-id",
	}

	expectedFormData, err := json.Marshal(logbooks.FormData)
	require.NoError(t, err)

	mock.ExpectBegin()

	mock.ExpectExec(`INSERT INTO log_books`).
		WithArgs(
			providedID,
			logbooks.EventID,
			logbooks.FromRole,
			logbooks.FromEntityID,
			logbooks.ToRole,
			logbooks.ToEntityID,
			logbooks.FieldTag,
			logbooks.StarRating,
			logbooks.OverallExperience,
			logbooks.ProfessionalSkills,
			logbooks.PersonalSkills,
			logbooks.ThankingNote,
			expectedFormData,
			logbooks.SubmittedAt,
			logbooks.CorrelationID,
			logbooks.EngagementSkills,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	mock.ExpectCommit()

	err = repository.InsertlogbooksWithLogBook(ctx, db, logbooks)

	assert.NoError(t, err)
	assert.Equal(t, providedID, logbooks.ID, "ID should remain the same as provided")
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestInsertlogbooksWithLogBook_BeginTransactionFailure(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	ctx := context.Background()
	logbooks := &repository.Logbooks{
		ID:            "test-id",
		EventID:       "test-event-id",
		FromRole:      "user",
		FromEntityID:  "test-from-entity",
		ToRole:        "pro",
		ToEntityID:    "test-to-entity",
		FieldTag:      "service",
		StarRating:    5,
		FormData:      map[string]interface{}{"key": "value"},
		SubmittedAt:   time.Now(),
		CorrelationID: "test-correlation-id",
	}

	mock.ExpectBegin().WillReturnError(sql.ErrConnDone)

	err = repository.InsertlogbooksWithLogBook(ctx, db, logbooks)

	assert.Error(t, err)
	assert.Equal(t, sql.ErrConnDone, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestInsertlogbooksWithLogBook_InsertFailure(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	ctx := context.Background()
	logbooks := &repository.Logbooks{
		ID:            "test-id",
		EventID:       "test-event-id",
		FromRole:      "user",
		FromEntityID:  "test-from-entity",
		ToRole:        "pro",
		ToEntityID:    "test-to-entity",
		FieldTag:      "service",
		StarRating:    5,
		FormData:      map[string]interface{}{"key": "value"},
		SubmittedAt:   time.Now(),
		CorrelationID: "test-correlation-id",
	}

	mock.ExpectBegin()

	mock.ExpectExec(`INSERT INTO log_books`).
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnError(sql.ErrNoRows)

	mock.ExpectRollback()

	err = repository.InsertlogbooksWithLogBook(ctx, db, logbooks)

	assert.Error(t, err)
	assert.Equal(t, sql.ErrNoRows, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestInsertlogbooksWithLogBook_CommitFailure(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	ctx := context.Background()
	logbooks := &repository.Logbooks{
		ID:            "test-id",
		EventID:       "test-event-id",
		FromRole:      "user",
		FromEntityID:  "test-from-entity",
		ToRole:        "pro",
		ToEntityID:    "test-to-entity",
		FieldTag:      "service",
		StarRating:    5,
		FormData:      map[string]interface{}{"key": "value"},
		SubmittedAt:   time.Now(),
		CorrelationID: "test-correlation-id",
	}

	mock.ExpectBegin()

	mock.ExpectExec(`INSERT INTO log_books`).
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	mock.ExpectCommit().WillReturnError(sql.ErrTxDone)

	err = repository.InsertlogbooksWithLogBook(ctx, db, logbooks)

	assert.Error(t, err)
	assert.Equal(t, sql.ErrTxDone, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestInsertlogbooksWithLogBook_WithNilFormData(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	ctx := context.Background()
	logbooks := &repository.Logbooks{
		ID:                 "test-id",
		EventID:            "test-event-id",
		FromRole:           "user",
		FromEntityID:       "test-from-entity",
		ToRole:             "pro",
		ToEntityID:         "test-to-entity",
		FieldTag:           "service",
		StarRating:         5,
		OverallExperience:  "Excellent",
		EngagementSkills:   "Outstanding",
		ProfessionalSkills: "Great",
		PersonalSkills:     "Amazing",
		ThankingNote:       "Thank you",
		FormData:           nil, // nil FormData
		SubmittedAt:        time.Now(),
		CorrelationID:      "test-correlation-id",
	}

	expectedFormData := []byte("null")

	mock.ExpectBegin()

	mock.ExpectExec(`INSERT INTO log_books`).
		WithArgs(
			logbooks.ID,
			logbooks.EventID,
			logbooks.FromRole,
			logbooks.FromEntityID,
			logbooks.ToRole,
			logbooks.ToEntityID,
			logbooks.FieldTag,
			logbooks.StarRating,
			logbooks.OverallExperience,
			logbooks.ProfessionalSkills,
			logbooks.PersonalSkills,
			logbooks.ThankingNote,
			expectedFormData,
			logbooks.SubmittedAt,
			logbooks.CorrelationID,
			logbooks.EngagementSkills,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	mock.ExpectCommit()

	err = repository.InsertlogbooksWithLogBook(ctx, db, logbooks)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestInsertlogbooksWithLogBook_WithEmptyStrings(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	ctx := context.Background()
	logbooks := &repository.Logbooks{
		ID:                 "test-id",
		EventID:            "",
		FromRole:           "",
		FromEntityID:       "test-from-entity",
		ToRole:             "",
		ToEntityID:         "test-to-entity",
		FieldTag:           "",
		StarRating:         0,
		OverallExperience:  "",
		EngagementSkills:   "",
		ProfessionalSkills: "",
		PersonalSkills:     "",
		ThankingNote:       "",
		FormData:           map[string]interface{}{},
		SubmittedAt:        time.Now(),
		CorrelationID:      "test-correlation-id",
	}

	expectedFormData, err := json.Marshal(logbooks.FormData)
	require.NoError(t, err)

	mock.ExpectBegin()

	mock.ExpectExec(`INSERT INTO log_books`).
		WithArgs(
			logbooks.ID,
			logbooks.EventID,
			logbooks.FromRole,
			logbooks.FromEntityID,
			logbooks.ToRole,
			logbooks.ToEntityID,
			logbooks.FieldTag,
			logbooks.StarRating,
			logbooks.OverallExperience,
			logbooks.ProfessionalSkills,
			logbooks.PersonalSkills,
			logbooks.ThankingNote,
			expectedFormData,
			logbooks.SubmittedAt,
			logbooks.CorrelationID,
			logbooks.EngagementSkills,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	mock.ExpectCommit()

	err = repository.InsertlogbooksWithLogBook(ctx, db, logbooks)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestInsertlogbooksWithLogBook_WithSpecialCharacters(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	ctx := context.Background()
	logbooks := &repository.Logbooks{
		ID:                 "test-id",
		EventID:            "test-event-id",
		FromRole:           "user",
		FromEntityID:       "test-from-entity",
		ToRole:             "pro",
		ToEntityID:         "test-to-entity",
		FieldTag:           "service",
		StarRating:         5,
		OverallExperience:  "Excellent! Very good service with special chars: @#$%^&*()",
		EngagementSkills:   "Outstanding communication skills",
		ProfessionalSkills: "Great work with unicode: αβγδε and emojis: 😊👍",
		PersonalSkills:     "Amazing personality with quotes: \"excellent\" and 'great'",
		ThankingNote:       "Thank you very much! Line breaks:\nNew line\tTab character",
		FormData:           map[string]interface{}{"special": "chars: @#$%", "unicode": "αβγδε", "emoji": "😊"},
		SubmittedAt:        time.Now(),
		CorrelationID:      "test-correlation-id",
	}

	expectedFormData, err := json.Marshal(logbooks.FormData)
	require.NoError(t, err)

	mock.ExpectBegin()

	mock.ExpectExec(`INSERT INTO log_books`).
		WithArgs(
			logbooks.ID,
			logbooks.EventID,
			logbooks.FromRole,
			logbooks.FromEntityID,
			logbooks.ToRole,
			logbooks.ToEntityID,
			logbooks.FieldTag,
			logbooks.StarRating,
			logbooks.OverallExperience,
			logbooks.ProfessionalSkills,
			logbooks.PersonalSkills,
			logbooks.ThankingNote,
			expectedFormData,
			logbooks.SubmittedAt,
			logbooks.CorrelationID,
			logbooks.EngagementSkills,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	mock.ExpectCommit()

	err = repository.InsertlogbooksWithLogBook(ctx, db, logbooks)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}
