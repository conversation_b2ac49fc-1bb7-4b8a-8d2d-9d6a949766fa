package rating

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/ratings/pkg/rabbitmq"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type mockLogger struct{}

func (m *mockLogger) Debug(msg string, fields ...vlog.Field) {}
func (m *mockLogger) Info(msg string, fields ...vlog.Field)  {}
func (m *mockLogger) Warn(msg string, fields ...vlog.Field)  {}
func (m *mockLogger) Error(msg string, fields ...vlog.Field) {}
func (m *mockLogger) Fatal(msg string, fields ...vlog.Field) {}
func (m *mockLogger) Panic(msg string, fields ...vlog.Field) {}
func (m *mockLogger) Flush() error {
	return nil
}
func (m *mockLogger) Log(level vlog.Level, msg string, fields ...vlog.Field) {}
func (m *mockLogger) With(fields ...vlog.Field) vlog.Logger {
	return m
}

func TestNewFactory(t *testing.T) {
	// db mock
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")

	// logger mock
	logger := &mockLogger{}

	// nil rabbitmq mock
	var mockRabbitMQ *rabbitmq.RabbitMQ = nil

	factory := NewFactory(sqlxDB, logger, mockRabbitMQ)

	assert.NotNil(t, factory, "Factory should not be nil")
	assert.Equal(t, sqlxDB, factory.db, "Database should be properly assigned")
	assert.Equal(t, logger, factory.logger, "Logger should be properly assigned")
	assert.Equal(t, mockRabbitMQ, factory.rabbitmq, "RabbitMQ should be properly assigned")
}

func TestNewFactory_WithRealRabbitMQ(t *testing.T) {
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")

	logger := &mockLogger{}

	var mockRabbitMQ *rabbitmq.RabbitMQ = nil

	// Execute the function
	factory := NewFactory(sqlxDB, logger, mockRabbitMQ)

	// Assertions
	assert.NotNil(t, factory, "Factory should not be nil")
	assert.Equal(t, sqlxDB, factory.db, "Database should be properly assigned")
	assert.Equal(t, logger, factory.logger, "Logger should be properly assigned")
	assert.Equal(t, mockRabbitMQ, factory.rabbitmq, "RabbitMQ should be properly assigned")

	assert.NotNil(t, factory.db, "Factory db field should be accessible")
	assert.NotNil(t, factory.logger, "Factory logger field should be accessible")
}

func TestFactory_Ratingsubscriber(t *testing.T) {
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")

	logger := &mockLogger{}

	factory := NewFactory(sqlxDB, logger, nil)

	queueName := "test-rating-queue"

	subscriber := factory.Ratingsubscriber(queueName)

	assert.NotNil(t, subscriber, "Subscriber should not be nil")
	assert.Equal(t, queueName, subscriber.QueueName(), "Queue name should be properly set")
	assert.Equal(t, sqlxDB.DB, subscriber.DB, "Database connection should be properly set")

	assert.NotNil(t, subscriber.DB, "Subscriber DB should not be nil")
	assert.Equal(t, queueName, subscriber.Queue, "Subscriber Queue field should match input")
}

func TestFactory_Ratingsubscriber_WithDifferentQueueNames(t *testing.T) {
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")

	logger := &mockLogger{}

	factory := NewFactory(sqlxDB, logger, nil)

	// different queue names for tests
	testCases := []struct {
		name      string
		queueName string
	}{
		{
			name:      "Standard queue name",
			queueName: "rating-queue",
		},
		{
			name:      "With special characters",
			queueName: "rating-queue-v2.test",
		},
		{
			name:      "Empty queue name",
			queueName: "",
		},
		{
			name:      "Queue with spaces",
			queueName: "rating queue with spaces",
		},
		{
			name:      "Long queue name",
			queueName: "very-long-rating-queue-name-for-testing-purposes-with-many-characters",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			subscriber := factory.Ratingsubscriber(tc.queueName)

			assert.NotNil(t, subscriber, "Subscriber should not be nil")
			assert.Equal(t, tc.queueName, subscriber.QueueName(), "Queue name should match input")
			assert.Equal(t, sqlxDB.DB, subscriber.DB, "Database connection should be properly set")
		})
	}
}

func TestFactory_Ratingsubscriber_DatabaseConnection(t *testing.T) {
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")

	logger := &mockLogger{}

	factory := NewFactory(sqlxDB, logger, nil)

	subscriber := factory.Ratingsubscriber("test-queue")

	assert.Equal(t, sqlxDB.DB, subscriber.DB, "Subscriber should have the underlying sql.DB connection")
	assert.NotEqual(t, sqlxDB, subscriber.DB, "Subscriber should not have the sqlx.DB wrapper")

	assert.Same(t, sqlxDB.DB, subscriber.DB, "Database connections should be the same instance")
}

func TestFactory_Ratingsubscriber_LoggerAssignment(t *testing.T) {
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")

	logger := &mockLogger{}

	factory := NewFactory(sqlxDB, logger, nil)

	subscriber := factory.Ratingsubscriber("test-queue")

	assert.NotNil(t, subscriber, "Subscriber should not be nil")

	assert.Equal(t, "test-queue", subscriber.QueueName())
}

func TestFactory_GetRatingOptionsHandler(t *testing.T) {
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")
	logger := &mockLogger{}
	factory := NewFactory(sqlxDB, logger, nil)

	t.Run("success", func(t *testing.T) {
		handler := factory.GetRatingOptionsHandler()
		assert.NotNil(t, handler, "Handler should not be nil")
	})

	t.Run("multiple_calls_return_different_instances", func(t *testing.T) {
		handler1 := factory.GetRatingOptionsHandler()
		handler2 := factory.GetRatingOptionsHandler()

		assert.NotNil(t, handler1)
		assert.NotNil(t, handler2)
		assert.NotSame(t, handler1, handler2, "Each call should return a new instance")
	})
}

func TestFactory_SubmitRatingHandler(t *testing.T) {
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")
	logger := &mockLogger{}

	// Create a mock RabbitMQ with Publisher
	mockRabbitMQ := &rabbitmq.RabbitMQ{
		Publisher: nil, // This will be nil but that's ok for testing factory creation
	}

	factory := NewFactory(sqlxDB, logger, mockRabbitMQ)

	t.Run("success", func(t *testing.T) {
		queueName := "test-submit-queue"
		handler := factory.SubmitRatingHandler(queueName)
		assert.NotNil(t, handler, "Handler should not be nil")
	})

	t.Run("different_queue_names", func(t *testing.T) {
		testCases := []string{
			"queue1",
			"queue2",
			"rating-submit-queue",
			"",
		}

		for _, queueName := range testCases {
			t.Run("queue_"+queueName, func(t *testing.T) {
				handler := factory.SubmitRatingHandler(queueName)
				assert.NotNil(t, handler, "Handler should not be nil for queue: %s", queueName)
			})
		}
	})

	t.Run("multiple_calls_return_different_instances", func(t *testing.T) {
		queueName := "test-queue"
		handler1 := factory.SubmitRatingHandler(queueName)
		handler2 := factory.SubmitRatingHandler(queueName)

		assert.NotNil(t, handler1)
		assert.NotNil(t, handler2)
		assert.NotSame(t, handler1, handler2, "Each call should return a new instance")
	})
}

func TestFactory_SubmitRatingHandler_WithNilRabbitMQ(t *testing.T) {
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")
	logger := &mockLogger{}
	factory := NewFactory(sqlxDB, logger, nil)

	t.Run("nil_rabbitmq_will_panic", func(t *testing.T) {
		queueName := "test-queue"

		// This will panic with nil RabbitMQ because we access rabbitmq.Publisher
		assert.Panics(t, func() {
			factory.SubmitRatingHandler(queueName)
		})
	})
}

func TestFactory_AllMethods_Integration(t *testing.T) {
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")
	logger := &mockLogger{}
	mockRabbitMQ := &rabbitmq.RabbitMQ{Publisher: nil}
	factory := NewFactory(sqlxDB, logger, mockRabbitMQ)

	t.Run("all_factory_methods_work", func(t *testing.T) {
		// Test GetRatingOptionsHandler
		getRatingHandler := factory.GetRatingOptionsHandler()
		assert.NotNil(t, getRatingHandler)

		// Test SubmitRatingHandler
		submitHandler := factory.SubmitRatingHandler("submit-queue")
		assert.NotNil(t, submitHandler)

		// Test DeleteRatingHandler
		deleteHandler := factory.DeleteRatingHandler("delete-queue")
		assert.NotNil(t, deleteHandler)

		// Test Ratingsubscriber
		subscriber := factory.Ratingsubscriber("subscriber-queue")
		assert.NotNil(t, subscriber)
		assert.Equal(t, "subscriber-queue", subscriber.QueueName())
		assert.Equal(t, sqlxDB.DB, subscriber.DB)

		// Test DeleteRatingSubscriber
		deleteSubscriber := factory.DeleteRatingSubscriber("delete-subscriber-queue")
		assert.NotNil(t, deleteSubscriber)
		assert.Equal(t, "delete-subscriber-queue", deleteSubscriber.QueueName())
		assert.Equal(t, sqlxDB.DB, deleteSubscriber.DB)
	})
}

func TestFactory_DeleteRatingHandler(t *testing.T) {
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")
	logger := &mockLogger{}

	// Create a mock RabbitMQ with Publisher
	mockRabbitMQ := &rabbitmq.RabbitMQ{
		Publisher: nil, // This will be nil but that's ok for testing factory creation
	}

	factory := NewFactory(sqlxDB, logger, mockRabbitMQ)

	t.Run("success", func(t *testing.T) {
		queueName := "test-delete-queue"
		handler := factory.DeleteRatingHandler(queueName)
		assert.NotNil(t, handler, "Handler should not be nil")
	})

	t.Run("different_queue_names", func(t *testing.T) {
		testCases := []string{
			"delete-queue1",
			"delete-queue2",
			"rating-delete-queue",
			"",
		}

		for _, queueName := range testCases {
			t.Run("queue_"+queueName, func(t *testing.T) {
				handler := factory.DeleteRatingHandler(queueName)
				assert.NotNil(t, handler, "Handler should not be nil for queue: %s", queueName)
			})
		}
	})

	t.Run("multiple_calls_return_different_instances", func(t *testing.T) {
		queueName := "test-delete-queue"
		handler1 := factory.DeleteRatingHandler(queueName)
		handler2 := factory.DeleteRatingHandler(queueName)

		assert.NotNil(t, handler1)
		assert.NotNil(t, handler2)
		assert.NotSame(t, handler1, handler2, "Each call should return a new instance")
	})
}

func TestFactory_DeleteRatingSubscriber(t *testing.T) {
	db, _, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	sqlxDB := sqlx.NewDb(db, "postgres")
	logger := &mockLogger{}
	factory := NewFactory(sqlxDB, logger, nil)

	t.Run("success", func(t *testing.T) {
		queueName := "test-delete-subscriber-queue"
		subscriber := factory.DeleteRatingSubscriber(queueName)
		assert.NotNil(t, subscriber, "Subscriber should not be nil")
		assert.Equal(t, queueName, subscriber.QueueName())
		assert.Equal(t, sqlxDB.DB, subscriber.DB)
	})

	t.Run("different_queue_names", func(t *testing.T) {
		testCases := []string{
			"delete-sub-queue1",
			"delete-sub-queue2",
			"rating-delete-subscriber",
			"",
		}

		for _, queueName := range testCases {
			t.Run("queue_"+queueName, func(t *testing.T) {
				subscriber := factory.DeleteRatingSubscriber(queueName)
				assert.NotNil(t, subscriber, "Subscriber should not be nil for queue: %s", queueName)
				assert.Equal(t, queueName, subscriber.QueueName())
			})
		}
	})

	t.Run("multiple_calls_return_different_instances", func(t *testing.T) {
		queueName := "test-delete-subscriber-queue"
		subscriber1 := factory.DeleteRatingSubscriber(queueName)
		subscriber2 := factory.DeleteRatingSubscriber(queueName)

		assert.NotNil(t, subscriber1)
		assert.NotNil(t, subscriber2)
		assert.NotSame(t, subscriber1, subscriber2, "Each call should return a new instance")
	})
}
