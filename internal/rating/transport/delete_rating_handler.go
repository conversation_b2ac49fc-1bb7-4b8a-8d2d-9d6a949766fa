package transport

import (
	"context"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/usecase"
)

type deleteRatingUsecase interface {
	Execute(ctx context.Context, input *usecase.DeleteRatingInput) (*usecase.DeleteRatingOutput, error)
}

type DeleteRating<PERSON>andler struct {
	usecase deleteRatingUsecase
}

func NewDeleteRatingHandler(usecase deleteRatingUsecase) *DeleteRatingHandler {
	return &DeleteRatingHandler{
		usecase: usecase,
	}
}

type DeleteRatingRequest struct {
	EventID string `json:"event_id"`
}

type DeleteRatingResponse struct {
	Success       bool   `json:"success"`
	CorrelationID string `json:"correlation_id"`
	Message       string `json:"message"`
	EventID       string `json:"event_id"`
}

func (h *DeleteRatingHandler) Handle() echo.HandlerFunc {
	return func(c echo.Context) error {
		var (
			ctx = c.Request().Context()
		)

		var input DeleteRatingRequest
		if err := c.Bind(&input); err != nil {
			return err
		}

		// Validate required fields
		if input.EventID == "" {
			return c.JSON(400, map[string]string{"error": "event_id is required"})
		}

		usecaseInput := &usecase.DeleteRatingInput{
			EventID: input.EventID,
		}

		result, err := h.usecase.Execute(ctx, usecaseInput)
		if err != nil {
			return err
		}

		response := &DeleteRatingResponse{
			Success:       result.Success,
			CorrelationID: result.CorrelationID,
			Message:       result.Message,
			EventID:       result.EventID,
		}

		return c.JSON(200, response)
	}
}
