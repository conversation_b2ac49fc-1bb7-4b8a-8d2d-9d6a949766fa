package transport_test

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/transport"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/usecase"
	"go.uber.org/mock/gomock"
)

// Mock usecase for testing
type mockSubmitRatingUsecase struct {
	ctrl     *gomock.Controller
	recorder *MockSubmitRatingUsecaseMockRecorder
}

type MockSubmitRatingUsecaseMockRecorder struct {
	mock *mockSubmitRatingUsecase
}

func NewMockSubmitRatingUsecase(ctrl *gomock.Controller) *mockSubmitRatingUsecase {
	mock := &mockSubmitRatingUsecase{ctrl: ctrl}
	mock.recorder = &MockSubmitRatingUsecaseMockRecorder{mock}
	return mock
}

func (m *mockSubmitRatingUsecase) EXPECT() *MockSubmitRatingUsecaseMockRecorder {
	return m.recorder
}

func (m *mockSubmitRatingUsecase) Execute(ctx context.Context, input *usecase.SubmitRatingInput) (*usecase.SubmitRatingOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Execute", ctx, input)
	ret0, _ := ret[0].(*usecase.SubmitRatingOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockSubmitRatingUsecaseMockRecorder) Execute(ctx, input interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Execute", reflect.TypeOf((*mockSubmitRatingUsecase)(nil).Execute), ctx, input)
}

func TestSubmitRatingHandler_Handle(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    map[string]interface{}
		expectedStatus int
		expectedError  string
		setupMock      func(*mockSubmitRatingUsecase)
	}{
		{
			name: "success",
			requestBody: map[string]interface{}{
				"event_id":            "test-event-id",
				"from_role":           "user",
				"from_entity_id":      "test-from-entity",
				"to_role":             "pro",
				"to_entity_id":        "test-to-entity",
				"field_tag":           "service",
				"star_rating":         5,
				"overall_experience":  "Excellent service",
				"professional_skills": "Great technical skills",
				"personal_skills":     "Amazing personality",
				"engagement_skills":   "Outstanding communication",
				"thanking_note":       "Thank you very much",
				"form_data":           map[string]interface{}{"key": "value"},
			},
			expectedStatus: 200,
			setupMock: func(m *mockSubmitRatingUsecase) {
				m.EXPECT().Execute(gomock.Any(), gomock.Any()).Return(&usecase.SubmitRatingOutput{
					Success:       true,
					CorrelationID: "test-correlation-id",
					Message:       "Rating submitted successfully",
				}, nil)
			},
		},
		{
			name: "success_minimal_fields",
			requestBody: map[string]interface{}{
				"event_id":       "test-event-id",
				"from_role":      "user",
				"from_entity_id": "test-from-entity",
				"to_role":        "pro",
				"to_entity_id":   "test-to-entity",
				"star_rating":    3,
			},
			expectedStatus: 200,
			setupMock: func(m *mockSubmitRatingUsecase) {
				m.EXPECT().Execute(gomock.Any(), gomock.Any()).Return(&usecase.SubmitRatingOutput{
					Success:       true,
					CorrelationID: "test-correlation-id",
					Message:       "Rating submitted successfully",
				}, nil)
			},
		},
		{
			name: "missing_event_id",
			requestBody: map[string]interface{}{
				"from_role":      "user",
				"from_entity_id": "test-from-entity",
				"to_role":        "pro",
				"to_entity_id":   "test-to-entity",
				"star_rating":    5,
			},
			expectedStatus: 400,
			expectedError:  "event_id is required",
			setupMock:      func(m *mockSubmitRatingUsecase) {},
		},
		{
			name: "missing_from_role",
			requestBody: map[string]interface{}{
				"event_id":       "test-event-id",
				"from_entity_id": "test-from-entity",
				"to_role":        "pro",
				"to_entity_id":   "test-to-entity",
				"star_rating":    5,
			},
			expectedStatus: 400,
			expectedError:  "from_role is required",
			setupMock:      func(m *mockSubmitRatingUsecase) {},
		},
		{
			name: "missing_from_entity_id",
			requestBody: map[string]interface{}{
				"event_id":     "test-event-id",
				"from_role":    "user",
				"to_role":      "pro",
				"to_entity_id": "test-to-entity",
				"star_rating":  5,
			},
			expectedStatus: 400,
			expectedError:  "from_entity_id is required",
			setupMock:      func(m *mockSubmitRatingUsecase) {},
		},
		{
			name: "missing_to_role",
			requestBody: map[string]interface{}{
				"event_id":       "test-event-id",
				"from_role":      "user",
				"from_entity_id": "test-from-entity",
				"to_entity_id":   "test-to-entity",
				"star_rating":    5,
			},
			expectedStatus: 400,
			expectedError:  "to_role is required",
			setupMock:      func(m *mockSubmitRatingUsecase) {},
		},
		{
			name: "missing_to_entity_id",
			requestBody: map[string]interface{}{
				"event_id":       "test-event-id",
				"from_role":      "user",
				"from_entity_id": "test-from-entity",
				"to_role":        "pro",
				"star_rating":    5,
			},
			expectedStatus: 400,
			expectedError:  "to_entity_id is required",
			setupMock:      func(m *mockSubmitRatingUsecase) {},
		},
		{
			name: "star_rating_too_low",
			requestBody: map[string]interface{}{
				"event_id":       "test-event-id",
				"from_role":      "user",
				"from_entity_id": "test-from-entity",
				"to_role":        "pro",
				"to_entity_id":   "test-to-entity",
				"star_rating":    0,
			},
			expectedStatus: 400,
			expectedError:  "star_rating must be between 1 and 5",
			setupMock:      func(m *mockSubmitRatingUsecase) {},
		},
		{
			name: "star_rating_too_high",
			requestBody: map[string]interface{}{
				"event_id":       "test-event-id",
				"from_role":      "user",
				"from_entity_id": "test-from-entity",
				"to_role":        "pro",
				"to_entity_id":   "test-to-entity",
				"star_rating":    6,
			},
			expectedStatus: 400,
			expectedError:  "star_rating must be between 1 and 5",
			setupMock:      func(m *mockSubmitRatingUsecase) {},
		},
		{
			name: "star_rating_negative",
			requestBody: map[string]interface{}{
				"event_id":       "test-event-id",
				"from_role":      "user",
				"from_entity_id": "test-from-entity",
				"to_role":        "pro",
				"to_entity_id":   "test-to-entity",
				"star_rating":    -1,
			},
			expectedStatus: 400,
			expectedError:  "star_rating must be between 1 and 5",
			setupMock:      func(m *mockSubmitRatingUsecase) {},
		},
		{
			name: "star_rating_boundary_1",
			requestBody: map[string]interface{}{
				"event_id":       "test-event-id",
				"from_role":      "user",
				"from_entity_id": "test-from-entity",
				"to_role":        "pro",
				"to_entity_id":   "test-to-entity",
				"star_rating":    1,
			},
			expectedStatus: 200,
			setupMock: func(m *mockSubmitRatingUsecase) {
				m.EXPECT().Execute(gomock.Any(), gomock.Any()).Return(&usecase.SubmitRatingOutput{
					Success:       true,
					CorrelationID: "test-correlation-id",
					Message:       "Rating submitted successfully",
				}, nil)
			},
		},
		{
			name: "star_rating_boundary_5",
			requestBody: map[string]interface{}{
				"event_id":       "test-event-id",
				"from_role":      "user",
				"from_entity_id": "test-from-entity",
				"to_role":        "pro",
				"to_entity_id":   "test-to-entity",
				"star_rating":    5,
			},
			expectedStatus: 200,
			setupMock: func(m *mockSubmitRatingUsecase) {
				m.EXPECT().Execute(gomock.Any(), gomock.Any()).Return(&usecase.SubmitRatingOutput{
					Success:       true,
					CorrelationID: "test-correlation-id",
					Message:       "Rating submitted successfully",
				}, nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockUsecase := NewMockSubmitRatingUsecase(ctrl)
			tt.setupMock(mockUsecase)

			handler := transport.NewSubmitRatingHandler(mockUsecase)

			e := echo.New()

			requestBody, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest(http.MethodPost, "/submit-rating", bytes.NewReader(requestBody))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Handle()(c)

			if tt.expectedStatus == 200 {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedStatus, rec.Code)

				var response transport.SubmitRatingResponse
				err = json.Unmarshal(rec.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.True(t, response.Success)
				assert.NotEmpty(t, response.CorrelationID)
				assert.Equal(t, "Rating submitted successfully", response.Message)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedStatus, rec.Code)

				var response map[string]string
				err = json.Unmarshal(rec.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedError, response["error"])
			}
		})
	}
}

func TestSubmitRatingHandler_Handle_UsecaseError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockUsecase := NewMockSubmitRatingUsecase(ctrl)
	handler := transport.NewSubmitRatingHandler(mockUsecase)

	t.Run("usecase_returns_error", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"event_id":       "test-event-id",
			"from_role":      "user",
			"from_entity_id": "test-from-entity",
			"to_role":        "pro",
			"to_entity_id":   "test-to-entity",
			"star_rating":    5,
		}

		usecaseError := errors.New("rabbitmq connection failed")
		mockUsecase.EXPECT().Execute(gomock.Any(), gomock.Any()).Return(nil, usecaseError)

		e := echo.New()
		requestBodyBytes, _ := json.Marshal(requestBody)
		req := httptest.NewRequest(http.MethodPost, "/submit-rating", bytes.NewReader(requestBodyBytes))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.Handle()(c)
		assert.Error(t, err)
		assert.Equal(t, usecaseError, err)
	})
}

func TestSubmitRatingHandler_Handle_InvalidJSON(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockUsecase := NewMockSubmitRatingUsecase(ctrl)
	handler := transport.NewSubmitRatingHandler(mockUsecase)

	t.Run("invalid_json", func(t *testing.T) {
		e := echo.New()
		req := httptest.NewRequest(http.MethodPost, "/submit-rating", bytes.NewReader([]byte("invalid json")))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.Handle()(c)
		assert.Error(t, err)
	})
}

func TestSubmitRatingHandler_Handle_EmptyFields(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    map[string]interface{}
		expectedStatus int
		expectedError  string
	}{
		{
			name: "empty_event_id",
			requestBody: map[string]interface{}{
				"event_id":       "",
				"from_role":      "user",
				"from_entity_id": "test-from-entity",
				"to_role":        "pro",
				"to_entity_id":   "test-to-entity",
				"star_rating":    5,
			},
			expectedStatus: 400,
			expectedError:  "event_id is required",
		},
		{
			name: "empty_from_role",
			requestBody: map[string]interface{}{
				"event_id":       "test-event-id",
				"from_role":      "",
				"from_entity_id": "test-from-entity",
				"to_role":        "pro",
				"to_entity_id":   "test-to-entity",
				"star_rating":    5,
			},
			expectedStatus: 400,
			expectedError:  "from_role is required",
		},
		{
			name: "empty_from_entity_id",
			requestBody: map[string]interface{}{
				"event_id":       "test-event-id",
				"from_role":      "user",
				"from_entity_id": "",
				"to_role":        "pro",
				"to_entity_id":   "test-to-entity",
				"star_rating":    5,
			},
			expectedStatus: 400,
			expectedError:  "from_entity_id is required",
		},
		{
			name: "empty_to_role",
			requestBody: map[string]interface{}{
				"event_id":       "test-event-id",
				"from_role":      "user",
				"from_entity_id": "test-from-entity",
				"to_role":        "",
				"to_entity_id":   "test-to-entity",
				"star_rating":    5,
			},
			expectedStatus: 400,
			expectedError:  "to_role is required",
		},
		{
			name: "empty_to_entity_id",
			requestBody: map[string]interface{}{
				"event_id":       "test-event-id",
				"from_role":      "user",
				"from_entity_id": "test-from-entity",
				"to_role":        "pro",
				"to_entity_id":   "",
				"star_rating":    5,
			},
			expectedStatus: 400,
			expectedError:  "to_entity_id is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockUsecase := NewMockSubmitRatingUsecase(ctrl)
			handler := transport.NewSubmitRatingHandler(mockUsecase)

			e := echo.New()

			requestBody, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest(http.MethodPost, "/submit-rating", bytes.NewReader(requestBody))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Handle()(c)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedStatus, rec.Code)

			var response map[string]string
			err = json.Unmarshal(rec.Body.Bytes(), &response)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedError, response["error"])
		})
	}
}

func TestNewSubmitRatingHandler(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockUsecase := NewMockSubmitRatingUsecase(ctrl)

	t.Run("success", func(t *testing.T) {
		handler := transport.NewSubmitRatingHandler(mockUsecase)
		assert.NotNil(t, handler)
	})

	t.Run("with_nil_usecase", func(t *testing.T) {
		handler := transport.NewSubmitRatingHandler(nil)
		assert.NotNil(t, handler)
	})
}
