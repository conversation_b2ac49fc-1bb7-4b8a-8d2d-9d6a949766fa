package transport_test

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/transport"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/usecase"
	"go.uber.org/mock/gomock"
)

// Mock usecase for testing delete rating handler
type mockDeleteRatingUsecase struct {
	ctrl     *gomock.Controller
	recorder *MockDeleteRatingUsecaseMockRecorder
}

type MockDeleteRatingUsecaseMockRecorder struct {
	mock *mockDeleteRatingUsecase
}

func NewMockDeleteRatingUsecase(ctrl *gomock.Controller) *mockDeleteRatingUsecase {
	mock := &mockDeleteRatingUsecase{ctrl: ctrl}
	mock.recorder = &MockDeleteRatingUsecaseMockRecorder{mock}
	return mock
}

func (m *mockDeleteRatingUsecase) EXPECT() *MockDeleteRatingUsecaseMockRecorder {
	return m.recorder
}

func (m *mockDeleteRatingUsecase) Execute(ctx context.Context, input *usecase.DeleteRatingInput) (*usecase.DeleteRatingOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Execute", ctx, input)
	ret0, _ := ret[0].(*usecase.DeleteRatingOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockDeleteRatingUsecaseMockRecorder) Execute(ctx, input interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Execute", reflect.TypeOf((*mockDeleteRatingUsecase)(nil).Execute), ctx, input)
}

func TestDeleteRatingHandler_Handle(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    map[string]interface{}
		expectedStatus int
		expectedError  string
		setupMock      func(*mockDeleteRatingUsecase)
	}{
		{
			name: "success",
			requestBody: map[string]interface{}{
				"event_id": "test-event-id",
			},
			expectedStatus: 200,
			setupMock: func(m *mockDeleteRatingUsecase) {
				m.EXPECT().Execute(gomock.Any(), gomock.Any()).Return(&usecase.DeleteRatingOutput{
					Success:      true,
					Message:      "Rating deleted successfully",
					EventID:      "test-event-id",
					DeletedCount: 1,
				}, nil)
			},
		},
		{
			name: "missing_event_id",
			requestBody: map[string]interface{}{
				"other_field": "value",
			},
			expectedStatus: 400,
			expectedError:  "event_id is required",
			setupMock:      func(m *mockDeleteRatingUsecase) {},
		},
		{
			name: "empty_event_id",
			requestBody: map[string]interface{}{
				"event_id": "",
			},
			expectedStatus: 400,
			expectedError:  "event_id is required",
			setupMock:      func(m *mockDeleteRatingUsecase) {},
		},
		{
			name: "null_event_id",
			requestBody: map[string]interface{}{
				"event_id": nil,
			},
			expectedStatus: 400,
			expectedError:  "event_id is required",
			setupMock:      func(m *mockDeleteRatingUsecase) {},
		},
		{
			name: "usecase_error",
			requestBody: map[string]interface{}{
				"event_id": "test-event-id",
			},
			expectedStatus: 0, // Error will be returned directly
			setupMock: func(m *mockDeleteRatingUsecase) {
				m.EXPECT().Execute(gomock.Any(), gomock.Any()).Return(nil, errors.New("usecase error"))
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockUsecase := NewMockDeleteRatingUsecase(ctrl)
			tt.setupMock(mockUsecase)

			handler := transport.NewDeleteRatingHandler(mockUsecase)

			e := echo.New()

			requestBody, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest(http.MethodDelete, "/delete-rating", bytes.NewReader(requestBody))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Handle()(c)

			if tt.expectedStatus == 200 {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedStatus, rec.Code)

				var response transport.DeleteRatingResponse
				err = json.Unmarshal(rec.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.True(t, response.Success)
				assert.Equal(t, "Rating deleted successfully", response.Message)
				assert.Equal(t, "test-event-id", response.EventID)
				assert.Equal(t, int64(1), response.DeletedCount)
			} else if tt.expectedStatus == 400 {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedStatus, rec.Code)

				var response map[string]string
				err = json.Unmarshal(rec.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedError, response["error"])
			} else if tt.expectedStatus == 0 {
				// Usecase error case
				assert.Error(t, err)
				assert.Equal(t, "usecase error", err.Error())
			}
		})
	}
}

func TestDeleteRatingHandler_Handle_InvalidJSON(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockUsecase := NewMockDeleteRatingUsecase(ctrl)
	handler := transport.NewDeleteRatingHandler(mockUsecase)

	t.Run("invalid_json", func(t *testing.T) {
		e := echo.New()
		req := httptest.NewRequest(http.MethodDelete, "/delete-rating", bytes.NewReader([]byte("invalid json")))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.Handle()(c)
		assert.Error(t, err)
	})
}

func TestDeleteRatingHandler_Handle_VerifyUsecaseInput(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockUsecase := NewMockDeleteRatingUsecase(ctrl)
	handler := transport.NewDeleteRatingHandler(mockUsecase)

	t.Run("verify_usecase_input_mapping", func(t *testing.T) {
		eventID := "test-event-id-123"
		requestBody := map[string]interface{}{
			"event_id": eventID,
		}

		// Capture the usecase input
		var capturedInput *usecase.DeleteRatingInput
		mockUsecase.EXPECT().Execute(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, input *usecase.DeleteRatingInput) (*usecase.DeleteRatingOutput, error) {
				capturedInput = input
				return &usecase.DeleteRatingOutput{
					Success:      true,
					Message:      "Rating deleted successfully",
					EventID:      eventID,
					DeletedCount: 1,
				}, nil
			})

		e := echo.New()
		requestBodyBytes, _ := json.Marshal(requestBody)
		req := httptest.NewRequest(http.MethodDelete, "/delete-rating", bytes.NewReader(requestBodyBytes))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.Handle()(c)
		assert.NoError(t, err)
		assert.Equal(t, 200, rec.Code)

		// Verify the usecase input was mapped correctly
		assert.NotNil(t, capturedInput)
		assert.Equal(t, eventID, capturedInput.EventID)
	})
}

func TestDeleteRatingHandler_Handle_ResponseStructure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockUsecase := NewMockDeleteRatingUsecase(ctrl)
	handler := transport.NewDeleteRatingHandler(mockUsecase)

	t.Run("verify_response_structure", func(t *testing.T) {
		eventID := "test-event-id"
		message := "Rating deleted successfully"

		requestBody := map[string]interface{}{
			"event_id": eventID,
		}

		mockUsecase.EXPECT().Execute(gomock.Any(), gomock.Any()).Return(&usecase.DeleteRatingOutput{
			Success:      true,
			Message:      message,
			EventID:      eventID,
			DeletedCount: 1,
		}, nil)

		e := echo.New()
		requestBodyBytes, _ := json.Marshal(requestBody)
		req := httptest.NewRequest(http.MethodDelete, "/delete-rating", bytes.NewReader(requestBodyBytes))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.Handle()(c)
		assert.NoError(t, err)
		assert.Equal(t, 200, rec.Code)

		var response transport.DeleteRatingResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.True(t, response.Success)
		assert.Equal(t, message, response.Message)
		assert.Equal(t, eventID, response.EventID)
		assert.Equal(t, int64(1), response.DeletedCount)
	})
}

func TestNewDeleteRatingHandler(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockUsecase := NewMockDeleteRatingUsecase(ctrl)

	t.Run("success", func(t *testing.T) {
		handler := transport.NewDeleteRatingHandler(mockUsecase)
		assert.NotNil(t, handler)
	})

	t.Run("with_nil_usecase", func(t *testing.T) {
		handler := transport.NewDeleteRatingHandler(nil)
		assert.NotNil(t, handler)
	})
}
