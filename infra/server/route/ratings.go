package route

import (
	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/ratings/internal/rating/transport"
)

type ratingFactory interface {
	GetRatingOptionsHandler() *transport.GetRatingOptionsHandler
	SubmitRatingHandler(queueName string) *transport.SubmitRatingHandler
	DeleteRatingHandler() *transport.DeleteRatingHandler
}

type ratingRouter struct {
	baseRoute *echo.Group
	factory   ratingFactory
	queueName string
}

func NewRatingRouter(e *echo.Group, factory ratingFactory, queueName string) *ratingRouter {
	return &ratingRouter{
		baseRoute: e,
		factory:   factory,
		queueName: queueName,
	}
}

func (r *ratingRouter) Route() {
	getRatingOptionsHandler := r.factory.GetRatingOptionsHandler()
	submitRatingHandler := r.factory.SubmitRatingHandler(r.queueName)
	deleteRatingHandler := r.factory.DeleteRatingHandler()

	group := r.baseRoute.Group("/ratings")
	group.POST("/options", getRatingOptionsHandler.Handle())
	group.POST("/submit", submitRatingHandler.Handle())
	group.DELETE("/delete", deleteRatingHandler.Handle())
}
