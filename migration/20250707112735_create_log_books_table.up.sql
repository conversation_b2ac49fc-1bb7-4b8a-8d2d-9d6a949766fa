-- Create log_books table
CREATE TABLE log_books (
    id UUID PRIMARY KEY,
    event_id VARCHAR(36) NOT NULL,
    from_role VARCHAR(50) NOT NULL,
    from_entity_id VARCHAR(36) NOT NULL,
    to_role VARCHAR(50) NOT NULL,
    to_entity_id VARCHAR(36) NOT NULL,
    field_tag VARCHAR(50),
    star_rating INT NOT NULL,
    overall_experience TEXT,
    engagement_skills TEXT,
    professional_skills TEXT,
    personal_skills TEXT,
    thanking_note TEXT,
    form_data JSONB,
    submitted_at TIMESTAMPTZ NOT NULL,
    correlation_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for log_books table
CREATE INDEX idx_log_books_event_id ON log_books(event_id);
CREATE INDEX idx_log_books_from_entity ON log_books(from_role, from_entity_id);
CREATE INDEX idx_log_books_to_entity ON log_books(to_role, to_entity_id);
CREATE INDEX idx_log_books_submitted_at ON log_books(submitted_at);